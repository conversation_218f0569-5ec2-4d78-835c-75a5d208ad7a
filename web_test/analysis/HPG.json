{"s": "HPG", "ad": "2025-05-24", "ltd": "2023-11-23", "p": {"c": 25850, "cv": -1350, "cp": -0.05}, "t": {"d": "<PERSON><PERSON><PERSON><PERSON>", "s": "trung bình", "c": "81.13%"}, "mc": "ranging", "r": {"r": "Bán", "s": "=== PHÂN TÍCH KỸ THUẬT HPG (2023-11-23) ===\n• <PERSON><PERSON><PERSON> thời gian: <PERSON><PERSON> tích hàng ngà<PERSON> (Daily)\n• <PERSON><PERSON><PERSON> hiện tại: 25,850 (-0.05%)\n• <PERSON><PERSON><PERSON> đang dao động quanh các đường trung bình SMA5 (25,860) và SMA20 (25,582)\n• Thị trường trong trạng thái sideway hoặc tích lũy"}, "bz": [{"p": "25,717", "c": "trung bình", "r": "Điểm hỗ trợ S2"}, {"p": "25,273", "c": "cao", "r": "Hỗ trợ mây Kumo của <PERSON>ku"}, {"p": "25,003", "c": "trung bình", "r": "Vùng hỗ trợ"}, {"p": "24,228", "c": "trung bình", "r": "Hỗ trợ <PERSON>-sen c<PERSON>a <PERSON>"}], "slz": [], "tpz": [{"p": "26,395", "c": "trung bình", "r": "<PERSON><PERSON><PERSON> k<PERSON>g cự"}], "rr": [{"bp": "25,273", "slp": "25,003", "tp": "26,395", "r": "4.15", "q": "Tuyệt vời"}, {"bp": "25,003", "slp": "24,455", "tp": "26,395", "r": "2.54", "q": "<PERSON><PERSON><PERSON>"}, {"bp": "25,717", "slp": "25,273", "tp": "26,395", "r": "1.53", "q": "Khá"}], "ma": [], "ti": [{"n": "RS(52W)", "v": null, "s": "N/A", "a": "<PERSON>hông đủ dữ liệu để đưa ra khuyến nghị.", "d": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "i": "<PERSON><PERSON>ông có thông tin."}, {"n": "RSI", "v": 50.0, "s": "Trung tính", "a": "RSI trung tính, quan sát thêm.", "d": "Chỉ số sức mạnh tương đối (14 ngày)", "i": "RSI đo lường tốc độ và sự thay đổi của biến động giá. Gi<PERSON> trị dưới 30 thường được xem là quá bán (tín hiệu mua), trên 70 là quá mua (t<PERSON> hiệ<PERSON> bán). Mức 50 là ngưỡng phân cách xu hướng lên/xuống."}, {"n": "MACD", "v": 24.434320515090803, "s": "<PERSON><PERSON>", "a": "Đ<PERSON>ờng MACD trên đườ<PERSON> tín hiệu, xu hướng tăng đang chiếm ưu thế", "d": "Moving Average Convergence Divergence (12,26,9)", "i": "MACD là sự khác biệt giữa EMA 12 và EMA 26, với đường tín hiệu là EMA 9 của MACD. Khi MACD cắt lên trên đường tín hiệu là dấu hiệu mua, cắt xuống dưới là dấu hiệu bán. Mức 0 là ngưỡng đảo chiều quan trọng."}, {"n": "ADX / +DI / -DI", "v": "15.08 / 26.12 / 28.60", "s": "Sideway", "a": "<PERSON><PERSON><PERSON> trường sideway, quan sát thêm", "d": "ADX đo sức mạnh xu hướng, +DI/-DI xác định hướng. ADX > 25 là xu hướng mạnh.", "i": "ADX đo độ mạnh của xu hướng, +DI > -DI là tăng, -DI > +DI là giảm. Kết hợp 3 chỉ báo để xác nhận tín hiệu giao dịch theo xu hướng."}, {"n": "MACD Histogram", "v": -221.6687093563332, "s": "Bán", "a": "<PERSON><PERSON><PERSON> lư<PERSON><PERSON> gi<PERSON>m đang mạnh lên, <PERSON><PERSON> tiê<PERSON> bán", "d": "MACD Histogram", "i": "MACD Histogram là sự chênh lệch giữa đường MACD và đường tín hiệu. Gi<PERSON> trị dương và tăng chỉ báo động lượng tăng mạnh, giá trị âm và giảm chỉ báo động lượng giảm mạnh. Khi histogram thay đổi chiều là dấu hiệu đầu tiên của đảo chiều."}, {"n": "STOCHRSI_fastk", "v": null, "s": "N/A", "a": "<PERSON>hông đủ dữ liệu để đưa ra khuyến nghị.", "d": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "i": "<PERSON><PERSON>ông có thông tin."}, {"n": "CCI", "v": 119.8819150301633, "s": "Bán", "a": "CCI > 100: <PERSON><PERSON><PERSON>, c<PERSON> b<PERSON>.", "d": "Commodity Channel Index (20)", "i": "CCI đo lường sự khác biệt giữa giá hiện tại và giá trung bình lịch sử. Gi<PERSON> trị dưới -100 là quá bán (tín hiệu mua), trên +100 là quá mua (t<PERSON> hiệu bán). <PERSON>ức ±200 là vùng cực đoan, thường dẫn đến đảo chiều mạnh."}, {"n": "WPR", "v": -41.67, "s": "Trung tính", "a": "Williams %<PERSON> trên -50, xu hư<PERSON>ng tăng nhẹ", "d": "Williams %R (14)", "i": "Williams %R là chỉ báo động lượng đo lường mức độ quá mua hoặc quá bán. <PERSON><PERSON><PERSON> trị từ -80 đến -100 là quá bán (tín hiệu mua), từ 0 đến -20 là quá mua (t<PERSON> hiệu bán). Thường đảo chiều sớm hơn RSI và Stochastic."}, {"n": "ULTOSC", "v": 57.47, "s": "Trung tính", "a": "Ultimate Oscillator trê<PERSON> 50, xu hư<PERSON>ng tăng nhẹ", "d": "Ultimate Oscillator (7,14,28)", "i": "Ultimate Oscillator kế<PERSON> h<PERSON><PERSON> ba kho<PERSON>ng thời gian <PERSON> (7, 14, 28) để giảm nhiễu và tạo tín hiệu chính xác hơn. Gi<PERSON> trị dưới 30 là quá bán, trên 70 là quá mua. <PERSON><PERSON> độ tin cậy cao khi xác nhận với xu hướng giá chung."}, {"n": "ROC", "v": 0.98, "s": "<PERSON><PERSON>", "a": "<PERSON><PERSON> <PERSON>, độ<PERSON> <PERSON><PERSON><PERSON>, cân nh<PERSON>c mua.", "d": "Rate of Change (14)", "i": "Rate of Change đo lường tốc độ thay đổi giá theo phần trăm trong 14 phiên. Gi<PERSON> trị dương cao cho thấy tốc độ tăng mạnh (có thể là quá mua), gi<PERSON> trị âm sâu cho thấy tốc độ giảm mạnh (có thể là quá bán). Là chỉ báo động lượng tốt để xác định điểm đảo chiều."}, {"n": "SAR", "v": 24178.96, "s": "Bán", "a": "G<PERSON><PERSON> đang nằm dưới điểm SAR, xu hướng giảm đang tiế<PERSON> diễn", "d": "Parabolic SAR", "i": "Parabolic SAR là chỉ báo theo xu hướng, dùng để xác định điểm dừng và đảo chiều. Điểm SAR nằm dưới giá là tín hiệu mua (xu hướng tăng), điểm SAR nằm trên giá là tín hiệu bán (xu hướng giảm). <PERSON>hi giá cắt qua điểm SAR, đó là tín hiệu đảo chiều mạnh."}, {"n": "OBV", "v": -100237317, "s": "Bán", "a": "<PERSON><PERSON><PERSON> xu h<PERSON>, <PERSON><PERSON><PERSON> b<PERSON>ạnh", "d": "On Balance Volume", "i": "OBV là chỉ báo cộng dồn khối lượng theo chiều giá. Khi OBV tăng cùng chiều với giá xác nhận xu hướng tăng, ngư<PERSON><PERSON> chiều với giá tạo phân kỳ cảnh báo đảo chiều."}, {"n": "AO", "v": 358.6764705882342, "s": "<PERSON><PERSON>", "a": "AO > 0: <PERSON><PERSON><PERSON>, c<PERSON> nh<PERSON>c mua.", "d": "Awesome Oscillator", "i": "Awesome Oscillator so sánh động lượng của 5 phiên gần nhất với 34 phiên. Khi AO vượt qua mức 0 từ dưới lên là tín hiệu mua, từ trên xuống là tín hiệu bán. AO tăng trong vùng dương xác nhận xu hướng tăng, giảm trong vùng âm xác nhận xu hướng giảm."}, {"n": "ADI", "v": 31895941302.315624, "s": "<PERSON><PERSON>", "a": "ADI tăng trong khi gi<PERSON>, ph<PERSON> kỳ tích c<PERSON>, chu<PERSON><PERSON> bị đảo chiều tăng", "d": "Accumulation Distribution Index", "i": "ADI kết hợp giá và khối lượng để đánh giá dòng tiền. Khi ADI tăng cùng chiều với giá là xác nhận xu hướng, ng<PERSON><PERSON><PERSON> chiều là dấu hiệu phân kỳ. <PERSON><PERSON> kỳ tích cực (g<PERSON><PERSON> nhưng ADI tăng) báo hiệu đảo chiều tăng, phân kỳ tiêu cực ngượ<PERSON> lại."}, {"n": "BB", "v": 0.0453218539566368, "s": "<PERSON><PERSON>", "a": "<PERSON><PERSON><PERSON> dưới dải gi<PERSON>a Bollinger Bands, xu hư<PERSON><PERSON> gi<PERSON> nhẹ", "d": "Bollinger Bands Width (20,2)", "i": "Bollinger Bands sử dụng độ lệch chuẩn để tạo dải biến động quanh SMA. Gi<PERSON> chạm dải trên thường là tín hiệu quá mua, chạm dải dưới là quá bán. <PERSON><PERSON>i hẹp thường báo hiệu sự tích lũy và sắp có breakout. BB Width đo lường độ rộng của dải, giá trị thấp dự báo biến động mạnh sắp xảy ra."}, {"n": "Momentum", "v": 300, "s": "<PERSON><PERSON>", "a": "<PERSON><PERSON><PERSON> lư<PERSON><PERSON> t<PERSON>ng mạnh, <PERSON><PERSON> tiên mua", "d": "Momentum (14)", "i": "Momentum đo lường tốc độ thay đổi giá trong 14 phiên. Gi<PERSON> trị dương cao thể hiện xu hướng tăng mạnh, giá trị âm sâu thể hiện xu hướng giảm mạnh. Khi Momentum đạt cực trị và bắt đầu đảo chiều là dấu hiệu sớm cho sự thay đổi xu hướng."}, {"n": "Bear Power", "v": 88.48835169728773, "s": "<PERSON><PERSON>", "a": "Bear Power dương, l<PERSON><PERSON> mua mạnh", "d": "Bear Power", "i": "Bear Power đo lường sức mạnh của phe bán bằng cách so sánh giá thấp nhất với EMA 13 ngày. Gi<PERSON> trị dương cho thấy người mua đang kiểm soát thị trường, gi<PERSON> trị âm cho thấy người bán đang kiểm soát. Khi Bear Power âm nhưng đang tăng dần là tín hiệu mua tiềm năng."}, {"n": "Stochastic", "v": 74.47089947089948, "s": "Trung tính", "a": "Stochastic trung tính, quan sát thêm", "d": "Stochastic Oscillator %K (14,3)", "i": "Stochastic Oscillator so sánh giá đóng cửa với phạm vi giá trong một khoảng thời gian nhất định. %K là đường ch<PERSON>h, %D là đường tín hiệu. G<PERSON><PERSON> trị dưới 20 là quá bán, trên 80 là quá mua. Tín hiệu mua khi %K cắt lên %D, bán khi %K cắt xuống %D."}]}