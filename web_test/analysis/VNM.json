{"s": "VNM", "ad": "2025-05-24", "ltd": "2023-11-23", "p": {"c": 68700, "cv": -700, "cp": -0.01008646}, "t": {"d": "<PERSON><PERSON><PERSON><PERSON>", "s": "trung bình", "c": "50.00%"}, "mc": "trending", "r": {"r": "Giảm tỷ trọng", "s": "=== PHÂN TÍCH KỸ THUẬT VNM (2023-11-23) ===\n• <PERSON><PERSON><PERSON> thời gian: <PERSON><PERSON> tích hàng ngày (Daily)\n• <PERSON><PERSON><PERSON> hiện tại: 68,700 (-0.01%)\n• <PERSON><PERSON><PERSON> đang nằm trên cả SMA5 (56,860) và SMA20 (56,890)\n• <PERSON> hướng tăng ngắn hạn đang chiếm ưu thế"}, "bz": [{"p": "55,867", "c": "trung bình", "r": "Điểm hỗ trợ S2"}, {"p": "53,390", "c": "trung bình", "r": "Vùng hỗ trợ"}, {"p": "50,580", "c": "trung bình", "r": "Vùng hỗ trợ"}], "slz": [], "tpz": [], "rr": [{"bp": "53,390", "slp": "50,580", "tp": "66,356", "r": "4.61", "q": "Tuyệt vời"}, {"bp": "55,867", "slp": "53,390", "tp": "66,356", "r": "4.24", "q": "Tuyệt vời"}, {"bp": "53,390", "slp": "50,580", "tp": "64,782", "r": "4.05", "q": "Tuyệt vời"}], "ma": [], "ti": [{"n": "RS(52W)", "v": null, "s": "N/A", "a": "<PERSON>hông đủ dữ liệu để đưa ra khuyến nghị.", "d": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "i": "<PERSON><PERSON>ông có thông tin."}, {"n": "RSI", "v": 50.0, "s": "Trung tính", "a": "RSI trung tính, quan sát thêm.", "d": "Chỉ số sức mạnh tương đối (14 ngày)", "i": "RSI đo lường tốc độ và sự thay đổi của biến động giá. Gi<PERSON> trị dưới 30 thường được xem là quá bán (tín hiệu mua), trên 70 là quá mua (t<PERSON> hiệ<PERSON> bán). Mức 50 là ngưỡng phân cách xu hướng lên/xuống."}, {"n": "MACD", "v": -455.67359052196844, "s": "Bán", "a": "<PERSON><PERSON>ờng MACD dư<PERSON>i đường tín hiệu, xu hướ<PERSON> gi<PERSON>m chiếm ưu thế", "d": "Moving Average Convergence Divergence (12,26,9)", "i": "MACD là sự khác biệt giữa EMA 12 và EMA 26, với đường tín hiệu là EMA 9 của MACD. Khi MACD cắt lên trên đường tín hiệu là dấu hiệu mua, cắt xuống dưới là dấu hiệu bán. Mức 0 là ngưỡng đảo chiều quan trọng."}, {"n": "ADX / +DI / -DI", "v": "28.17 / 16.26 / 28.95", "s": "<PERSON><PERSON><PERSON><PERSON>nh", "a": "<PERSON> g<PERSON><PERSON>, <PERSON><PERSON> ti<PERSON><PERSON> bán", "d": "ADX đo sức mạnh xu hướng, +DI/-DI xác định hướng. ADX > 25 là xu hướng mạnh.", "i": "ADX đo độ mạnh của xu hướng, +DI > -DI là tăng, -DI > +DI là giảm. Kết hợp 3 chỉ báo để xác nhận tín hiệu giao dịch theo xu hướng."}, {"n": "MACD Histogram", "v": -298.66887440461784, "s": "Bán", "a": "<PERSON><PERSON><PERSON> lư<PERSON><PERSON> gi<PERSON>m đang mạnh lên, <PERSON><PERSON> tiê<PERSON> bán", "d": "MACD Histogram", "i": "MACD Histogram là sự chênh lệch giữa đường MACD và đường tín hiệu. Gi<PERSON> trị dương và tăng chỉ báo động lượng tăng mạnh, giá trị âm và giảm chỉ báo động lượng giảm mạnh. Khi histogram thay đổi chiều là dấu hiệu đầu tiên của đảo chiều."}, {"n": "STOCHRSI_fastk", "v": null, "s": "N/A", "a": "<PERSON>hông đủ dữ liệu để đưa ra khuyến nghị.", "d": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "i": "<PERSON><PERSON>ông có thông tin."}, {"n": "CCI", "v": -61.93746242445174, "s": "Trung tính", "a": "<PERSON>I dương, xu hướng tăng nhẹ", "d": "Commodity Channel Index (20)", "i": "CCI đo lường sự khác biệt giữa giá hiện tại và giá trung bình lịch sử. Gi<PERSON> trị dưới -100 là quá bán (tín hiệu mua), trên +100 là quá mua (t<PERSON> hiệu bán). <PERSON>ức ±200 là vùng cực đoan, thường dẫn đến đảo chiều mạnh."}, {"n": "WPR", "v": -96.15, "s": "<PERSON><PERSON>", "a": "Williams %R trong vùng quá bán, cân nhắc mua.", "d": "Williams %R (14)", "i": "Williams %R là chỉ báo động lượng đo lường mức độ quá mua hoặc quá bán. <PERSON><PERSON><PERSON> trị từ -80 đến -100 là quá bán (tín hiệu mua), từ 0 đến -20 là quá mua (t<PERSON> hiệu bán). Thường đảo chiều sớm hơn RSI và Stochastic."}, {"n": "ULTOSC", "v": 34.69, "s": "Trung tính", "a": "Ultimate Oscillator trê<PERSON> 50, xu hư<PERSON>ng tăng nhẹ", "d": "Ultimate Oscillator (7,14,28)", "i": "Ultimate Oscillator kế<PERSON> h<PERSON><PERSON> ba kho<PERSON>ng thời gian <PERSON> (7, 14, 28) để giảm nhiễu và tạo tín hiệu chính xác hơn. Gi<PERSON> trị dưới 30 là quá bán, trên 70 là quá mua. <PERSON><PERSON> độ tin cậy cao khi xác nhận với xu hướng giá chung."}, {"n": "ROC", "v": -0.35, "s": "Bán", "a": "<PERSON><PERSON>, độ<PERSON> l<PERSON>, cân <PERSON>h<PERSON> bán.", "d": "Rate of Change (14)", "i": "Rate of Change đo lường tốc độ thay đổi giá theo phần trăm trong 14 phiên. Gi<PERSON> trị dương cao cho thấy tốc độ tăng mạnh (có thể là quá mua), gi<PERSON> trị âm sâu cho thấy tốc độ giảm mạnh (có thể là quá bán). Là chỉ báo động lượng tốt để xác định điểm đảo chiều."}, {"n": "SAR", "v": 54750.47, "s": "Bán", "a": "G<PERSON><PERSON> đang nằm dưới điểm SAR, xu hướng giảm đang tiế<PERSON> diễn", "d": "Parabolic SAR", "i": "Parabolic SAR là chỉ báo theo xu hướng, dùng để xác định điểm dừng và đảo chiều. Điểm SAR nằm dưới giá là tín hiệu mua (xu hướng tăng), điểm SAR nằm trên giá là tín hiệu bán (xu hướng giảm). <PERSON>hi giá cắt qua điểm SAR, đó là tín hiệu đảo chiều mạnh."}, {"n": "OBV", "v": -105512226, "s": "Bán", "a": "<PERSON><PERSON><PERSON> xu h<PERSON>, <PERSON><PERSON><PERSON> b<PERSON>ạnh", "d": "On Balance Volume", "i": "OBV là chỉ báo cộng dồn khối lượng theo chiều giá. Khi OBV tăng cùng chiều với giá xác nhận xu hướng tăng, ngư<PERSON><PERSON> chiều với giá tạo phân kỳ cảnh báo đảo chiều."}, {"n": "AO", "v": -593.0533529411769, "s": "Bán", "a": "AO < 0: <PERSON><PERSON><PERSON>, c<PERSON><PERSON> bán.", "d": "Awesome Oscillator", "i": "Awesome Oscillator so sánh động lượng của 5 phiên gần nhất với 34 phiên. Khi AO vượt qua mức 0 từ dưới lên là tín hiệu mua, từ trên xuống là tín hiệu bán. AO tăng trong vùng dương xác nhận xu hướng tăng, giảm trong vùng âm xác nhận xu hướng giảm."}, {"n": "ADI", "v": 5280052895.672301, "s": "<PERSON><PERSON>", "a": "ADI tăng trong khi gi<PERSON>, ph<PERSON> kỳ tích c<PERSON>, chu<PERSON><PERSON> bị đảo chiều tăng", "d": "Accumulation Distribution Index", "i": "ADI kết hợp giá và khối lượng để đánh giá dòng tiền. Khi ADI tăng cùng chiều với giá là xác nhận xu hướng, ng<PERSON><PERSON><PERSON> chiều là dấu hiệu phân kỳ. <PERSON><PERSON> kỳ tích cực (g<PERSON><PERSON> nhưng ADI tăng) báo hiệu đảo chiều tăng, phân kỳ tiêu cực ngượ<PERSON> lại."}, {"n": "BB", "v": 0.05349524381320955, "s": "Trung tính", "a": "<PERSON><PERSON><PERSON> dao động quanh dải gi<PERSON>, thị trường sideway", "d": "Bollinger Bands Width (20,2)", "i": "Bollinger Bands sử dụng độ lệch chuẩn để tạo dải biến động quanh SMA. Gi<PERSON> chạm dải trên thường là tín hiệu quá mua, chạm dải dưới là quá bán. <PERSON><PERSON>i hẹp thường báo hiệu sự tích lũy và sắp có breakout. BB Width đo lường độ rộng của dải, giá trị thấp dự báo biến động mạnh sắp xảy ra."}, {"n": "Momentum", "v": -1400, "s": "Bán", "a": "<PERSON><PERSON><PERSON> lượ<PERSON> giảm r<PERSON><PERSON> m<PERSON>nh, theo chiều xu hướng giảm", "d": "Momentum (14)", "i": "Momentum đo lường tốc độ thay đổi giá trong 14 phiên. Gi<PERSON> trị dương cao thể hiện xu hướng tăng mạnh, giá trị âm sâu thể hiện xu hướng giảm mạnh. Khi Momentum đạt cực trị và bắt đầu đảo chiều là dấu hiệu sớm cho sự thay đổi xu hướng."}, {"n": "Bear Power", "v": -750.450261260914, "s": "Bán", "a": "Bear Power âm mạnh, <PERSON><PERSON><PERSON> b<PERSON>nh, <PERSON><PERSON> thế thu<PERSON><PERSON> về bên bán", "d": "Bear Power", "i": "Bear Power đo lường sức mạnh của phe bán bằng cách so sánh giá thấp nhất với EMA 13 ngày. Gi<PERSON> trị dương cho thấy người mua đang kiểm soát thị trường, gi<PERSON> trị âm cho thấy người bán đang kiểm soát. Khi Bear Power âm nhưng đang tăng dần là tín hiệu mua tiềm năng."}, {"n": "Stochastic", "v": 26.22863247863248, "s": "Trung tính", "a": "Stochastic trung tính, quan sát thêm", "d": "Stochastic Oscillator %K (14,3)", "i": "Stochastic Oscillator so sánh giá đóng cửa với phạm vi giá trong một khoảng thời gian nhất định. %K là đường ch<PERSON>h, %D là đường tín hiệu. G<PERSON><PERSON> trị dưới 20 là quá bán, trên 80 là quá mua. Tín hiệu mua khi %K cắt lên %D, bán khi %K cắt xuống %D."}]}