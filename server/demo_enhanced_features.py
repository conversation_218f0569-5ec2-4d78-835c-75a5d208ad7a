#!/usr/bin/env python3
"""
Demo script showcasing the enhanced StockPal features.

This script demonstrates the new ML analytics, backtesting, and caching capabilities.
"""

import sys
import logging
from datetime import datetime
from pathlib import Path

# Add the server directory to Python path
server_dir = Path(__file__).parent
sys.path.insert(0, str(server_dir))

from refactored_main import StockPalApplication

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def demo_basic_analysis():
    """Demonstrate basic stock analysis."""
    print("\n" + "="*60)
    print("BASIC STOCK ANALYSIS DEMO")
    print("="*60)
    
    app = StockPalApplication()
    test_symbols = ["VIC", "VNM", "HPG"]
    
    for symbol in test_symbols:
        print(f"\n📊 Analyzing {symbol}...")
        
        try:
            # Fetch data first
            success = app.fetch_stock_data(symbol, days=90)
            if not success:
                print(f"   ❌ Failed to fetch data for {symbol}")
                continue
            
            # Perform analysis
            analysis = app.analyze_stock(symbol, days_back=90)
            if analysis:
                print(f"   💰 Current Price: {analysis['current_price']:.2f}")
                print(f"   📈 Recommendation: {analysis['recommendation']}")
                print(f"   📊 Trend: {analysis['trend']['direction']} ({analysis['trend']['strength']:.1%})")
                print(f"   🎯 Confidence: {analysis['confidence_score']:.1f}%")
                
                if analysis.get('buy_zones'):
                    print(f"   🟢 Buy Zones: {len(analysis['buy_zones'])} identified")
                if analysis.get('stop_loss_zones'):
                    print(f"   🔴 Stop Loss Zones: {len(analysis['stop_loss_zones'])} identified")
            else:
                print(f"   ❌ Analysis failed for {symbol}")
                
        except Exception as e:
            print(f"   💥 Error analyzing {symbol}: {str(e)}")


def demo_ml_predictions():
    """Demonstrate ML-based predictions."""
    print("\n" + "="*60)
    print("ML PREDICTIONS DEMO")
    print("="*60)
    
    app = StockPalApplication()
    test_symbols = ["VIC", "VNM"]
    
    for symbol in test_symbols:
        print(f"\n🤖 ML Prediction for {symbol}...")
        
        try:
            prediction = app.get_ml_prediction(symbol, horizon_days=5)
            if prediction:
                current = prediction['current_price']
                predicted = prediction['predicted_price']
                change = ((predicted - current) / current) * 100
                
                print(f"   💰 Current Price: {current:.2f}")
                print(f"   🔮 Predicted Price (5 days): {predicted:.2f}")
                print(f"   📊 Expected Change: {change:+.2f}%")
                print(f"   🎯 Confidence: {prediction['confidence']:.1%}")
                print(f"   📈 Trend Direction: {prediction['trend_direction']}")
                print(f"   💪 Trend Strength: {prediction['trend_strength']:.1%}")
                print(f"   🎯 Key Levels: {prediction['key_levels'][:3]}")  # Show first 3 levels
            else:
                print(f"   ❌ ML prediction failed for {symbol}")
                
        except Exception as e:
            print(f"   💥 Error getting ML prediction for {symbol}: {str(e)}")


def demo_backtesting():
    """Demonstrate backtesting capabilities."""
    print("\n" + "="*60)
    print("BACKTESTING DEMO")
    print("="*60)
    
    app = StockPalApplication()
    test_symbols = ["VIC", "HPG"]
    
    # Test different strategy parameters
    strategies = [
        {"name": "Conservative", "stop_loss": 0.03, "take_profit": 0.08},
        {"name": "Aggressive", "stop_loss": 0.07, "take_profit": 0.15},
    ]
    
    for symbol in test_symbols:
        print(f"\n📈 Backtesting {symbol}...")
        
        for strategy in strategies:
            print(f"\n   🎯 Strategy: {strategy['name']}")
            
            try:
                strategy_params = {
                    "stop_loss": strategy["stop_loss"],
                    "take_profit": strategy["take_profit"]
                }
                
                backtest = app.run_backtest(symbol, strategy_params)
                if backtest:
                    print(f"      💰 Total Return: {backtest['total_return_percent']:+.2f}%")
                    print(f"      📊 Total Trades: {backtest['total_trades']}")
                    print(f"      🎯 Win Rate: {backtest['win_rate']:.1f}%")
                    print(f"      📈 Sharpe Ratio: {backtest['sharpe_ratio']:.2f}")
                    print(f"      📉 Max Drawdown: {backtest['max_drawdown_percent']:.2f}%")
                    print(f"      💵 Final Capital: ${backtest['final_capital']:,.0f}")
                    print(f"      🔄 Signals Used: {backtest['signals_count']}")
                else:
                    print(f"      ❌ Backtest failed for {symbol}")
                    
            except Exception as e:
                print(f"      💥 Error backtesting {symbol}: {str(e)}")


def demo_cache_performance():
    """Demonstrate caching performance."""
    print("\n" + "="*60)
    print("CACHE PERFORMANCE DEMO")
    print("="*60)
    
    app = StockPalApplication()
    
    # Get initial cache stats
    print("\n📊 Initial Cache Statistics:")
    stats = app.get_cache_stats()
    print(f"   Hit Rate: {stats.get('hit_rate_percent', 0):.1f}%")
    print(f"   Total Files: {stats.get('total_files', 0)}")
    print(f"   Total Size: {stats.get('total_size_mb', 0):.1f} MB")
    
    # Demonstrate cache warming
    print("\n🔥 Warming cache with data fetches...")
    test_symbols = ["VIC", "VNM", "HPG", "TCB"]
    
    import time
    start_time = time.time()
    
    for symbol in test_symbols:
        try:
            app.fetch_stock_data(symbol, days=60)
            print(f"   ✅ Cached data for {symbol}")
        except Exception as e:
            print(f"   ❌ Failed to cache {symbol}: {str(e)}")
    
    cache_time = time.time() - start_time
    print(f"   ⏱️  Cache warming took: {cache_time:.2f} seconds")
    
    # Get updated cache stats
    print("\n📊 Updated Cache Statistics:")
    stats = app.get_cache_stats()
    print(f"   Hit Rate: {stats.get('hit_rate_percent', 0):.1f}%")
    print(f"   Total Files: {stats.get('total_files', 0)}")
    print(f"   Total Size: {stats.get('total_size_mb', 0):.1f} MB")
    
    # Show category breakdown
    if 'categories' in stats:
        print("\n📁 Cache by Category:")
        for category, cat_stats in stats['categories'].items():
            print(f"   {category}: {cat_stats['file_count']} files, {cat_stats['size_bytes']/1024:.1f} KB")
    
    # Demonstrate cache cleanup
    print("\n🧹 Cleaning up expired cache entries...")
    cleanup = app.cleanup_cache()
    print(f"   🗑️  Cleaned up: {cleanup['cleaned_entries']} entries")


def demo_comparison_analysis():
    """Demonstrate stock comparison capabilities."""
    print("\n" + "="*60)
    print("STOCK COMPARISON DEMO")
    print("="*60)
    
    app = StockPalApplication()
    symbols = ["VIC", "VNM", "HPG"]
    
    print(f"\n🔍 Comparing stocks: {', '.join(symbols)}")
    
    try:
        comparison = app.compare_stocks(symbols)
        if comparison:
            print("\n📊 Comparison Results:")
            
            if 'rankings' in comparison:
                rankings = comparison['rankings']
                
                if 'buy_recommendations' in rankings:
                    buy_recs = rankings['buy_recommendations']
                    print(f"   🟢 Buy Recommendations: {buy_recs}")
                
                if 'risk_scores' in rankings:
                    risk_scores = rankings['risk_scores']
                    print(f"   ⚠️  Risk Ranking: {risk_scores}")
                
                if 'performance_scores' in rankings:
                    perf_scores = rankings['performance_scores']
                    print(f"   📈 Performance Ranking: {perf_scores}")
            
            if 'summary' in comparison:
                summary = comparison['summary']
                print(f"\n📋 Summary:")
                print(f"   Total symbols analyzed: {summary.get('total_symbols', 0)}")
                print(f"   Average confidence: {summary.get('average_confidence', 0):.1f}%")
        else:
            print("   ❌ Comparison failed")
            
    except Exception as e:
        print(f"   💥 Error in comparison: {str(e)}")


def main():
    """Run all demos."""
    print("🚀 StockPal Enhanced Features Demo")
    print("=" * 60)
    print("This demo showcases the new ML analytics, backtesting, and caching capabilities.")
    
    try:
        # Run all demo sections
        demo_basic_analysis()
        demo_ml_predictions()
        demo_backtesting()
        demo_cache_performance()
        demo_comparison_analysis()
        
        print("\n" + "="*60)
        print("✅ DEMO COMPLETED SUCCESSFULLY")
        print("="*60)
        print("\nKey Features Demonstrated:")
        print("• 📊 Enhanced stock analysis with ML insights")
        print("• 🤖 Machine learning-based price predictions")
        print("• 📈 Comprehensive strategy backtesting")
        print("• ⚡ High-performance caching system")
        print("• 🔍 Multi-stock comparison analysis")
        print("\nThe new StockPal architecture provides:")
        print("• Better performance through intelligent caching")
        print("• Advanced analytics with ML predictions")
        print("• Robust backtesting for strategy validation")
        print("• Clean, maintainable code architecture")
        
    except Exception as e:
        print(f"\n💥 Demo failed with error: {str(e)}")
        logger.error("Demo failed", exc_info=True)
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
