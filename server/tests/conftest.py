"""
Pytest configuration and fixtures for StockPal tests.

This module provides common fixtures and configuration for all tests.
"""

import pytest
import tempfile
import shutil
from datetime import datetime, timedelta
from typing import List
from pathlib import Path

from shared.models.stock_models import PricePoint, DataProvider
from infrastructure.cache.cache_service import CacheService
from infrastructure.cache.cache_manager import CacheManager
from infrastructure.repositories.price_repository import SqlitePriceRepository
from infrastructure.repositories.symbol_repository import SqliteSymbolRepository
from infrastructure.external.data_fetcher import ExternalDataFetcher
from core.services.data_service import DataService
from core.services.analysis_service import AnalysisService
from core.processors.indicator_processor import IndicatorProcessor
from core.analytics.ml_analytics_service import MLAnalyticsService
from core.analytics.backtesting_service import BacktestingService


@pytest.fixture
def temp_dir():
    """Create a temporary directory for tests."""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir)


@pytest.fixture
def cache_service(temp_dir):
    """Create a cache service for testing."""
    return CacheService(cache_dir=temp_dir, default_ttl=3600)


@pytest.fixture
def cache_manager(cache_service):
    """Create a cache manager for testing."""
    return CacheManager(cache_service)


@pytest.fixture
def sample_prices() -> List[PricePoint]:
    """Generate sample price data for testing."""
    prices = []
    base_price = 100.0
    base_timestamp = int(datetime.now().timestamp()) - (100 * 24 * 3600)  # 100 days ago
    
    for i in range(100):
        # Simulate price movement with some randomness
        price_change = (i % 10 - 5) * 0.01  # -5% to +5% change
        current_price = base_price * (1 + price_change)
        
        price_point = PricePoint(
            timestamp=base_timestamp + (i * 24 * 3600),
            open_price=current_price * 0.99,
            high_price=current_price * 1.02,
            low_price=current_price * 0.98,
            close_price=current_price,
            volume=1000000 + (i * 10000),
            change_price=current_price - base_price,
            change_percent=((current_price - base_price) / base_price) * 100,
            symbol="TEST"
        )
        prices.append(price_point)
        base_price = current_price
    
    return prices


@pytest.fixture
def sample_volatile_prices() -> List[PricePoint]:
    """Generate volatile price data for testing."""
    prices = []
    base_price = 50.0
    base_timestamp = int(datetime.now().timestamp()) - (50 * 24 * 3600)  # 50 days ago
    
    for i in range(50):
        # Simulate high volatility
        price_change = (i % 6 - 3) * 0.05  # -15% to +15% change
        current_price = base_price * (1 + price_change)
        
        price_point = PricePoint(
            timestamp=base_timestamp + (i * 24 * 3600),
            open_price=current_price * 0.95,
            high_price=current_price * 1.08,
            low_price=current_price * 0.92,
            close_price=current_price,
            volume=500000 + (i * 5000),
            change_price=current_price - base_price,
            change_percent=((current_price - base_price) / base_price) * 100,
            symbol="VOLATILE"
        )
        prices.append(price_point)
        base_price = current_price
    
    return prices


@pytest.fixture
def sample_trending_up_prices() -> List[PricePoint]:
    """Generate uptrending price data for testing."""
    prices = []
    base_price = 75.0
    base_timestamp = int(datetime.now().timestamp()) - (60 * 24 * 3600)  # 60 days ago
    
    for i in range(60):
        # Simulate uptrend with some noise
        trend_change = 0.01  # 1% daily trend
        noise = (i % 4 - 2) * 0.005  # Small random noise
        total_change = trend_change + noise
        current_price = base_price * (1 + total_change)
        
        price_point = PricePoint(
            timestamp=base_timestamp + (i * 24 * 3600),
            open_price=current_price * 0.995,
            high_price=current_price * 1.01,
            low_price=current_price * 0.99,
            close_price=current_price,
            volume=800000 + (i * 8000),
            change_price=current_price - base_price,
            change_percent=((current_price - base_price) / base_price) * 100,
            symbol="UPTREND"
        )
        prices.append(price_point)
        base_price = current_price
    
    return prices


@pytest.fixture
def price_repository(temp_dir):
    """Create a price repository for testing."""
    # Use a temporary database file
    db_path = Path(temp_dir) / "test_stock_data.db"
    return SqlitePriceRepository(db_path=str(db_path))


@pytest.fixture
def symbol_repository(temp_dir):
    """Create a symbol repository for testing."""
    db_path = Path(temp_dir) / "test_stock_data.db"
    return SqliteSymbolRepository(db_path=str(db_path))


@pytest.fixture
def data_fetcher():
    """Create a data fetcher for testing."""
    return ExternalDataFetcher()


@pytest.fixture
def indicator_processor():
    """Create an indicator processor for testing."""
    return IndicatorProcessor()


@pytest.fixture
def data_service(price_repository, symbol_repository, data_fetcher):
    """Create a data service for testing."""
    return DataService(
        price_repository=price_repository,
        symbol_repository=symbol_repository,
        data_fetcher=data_fetcher
    )


@pytest.fixture
def analysis_service(data_service, indicator_processor):
    """Create an analysis service for testing."""
    return AnalysisService(
        data_service=data_service,
        indicator_processor=indicator_processor
    )


@pytest.fixture
def ml_analytics_service():
    """Create an ML analytics service for testing."""
    return MLAnalyticsService()


@pytest.fixture
def backtesting_service():
    """Create a backtesting service for testing."""
    return BacktestingService(initial_capital=100000.0, commission_rate=0.0015)


@pytest.fixture
def test_symbols():
    """Provide test symbols for testing."""
    return ["VIC", "VNM", "HPG", "TCB", "FPT"]


@pytest.fixture
def test_providers():
    """Provide test data providers."""
    return [DataProvider.SSI, DataProvider.VIETSTOCK, DataProvider.CAFEF]


# Test data validation helpers
def validate_price_point(price_point: PricePoint) -> bool:
    """Validate a price point for testing."""
    return (
        price_point.timestamp > 0 and
        price_point.open_price > 0 and
        price_point.high_price >= price_point.open_price and
        price_point.low_price <= price_point.open_price and
        price_point.close_price > 0 and
        price_point.volume >= 0
    )


def validate_price_sequence(prices: List[PricePoint]) -> bool:
    """Validate a sequence of price points."""
    if not prices:
        return False
    
    for i, price in enumerate(prices):
        if not validate_price_point(price):
            return False
        
        # Check timestamp ordering
        if i > 0 and price.timestamp <= prices[i-1].timestamp:
            return False
    
    return True


# Test configuration
pytest_plugins = []
