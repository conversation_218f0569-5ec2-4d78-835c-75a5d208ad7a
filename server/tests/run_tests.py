"""
Test runner for StockPal application.

This script runs the complete test suite including unit tests, integration tests,
and performance benchmarks.
"""

import sys
import os
import pytest
import time
from pathlib import Path

# Add the server directory to Python path
server_dir = Path(__file__).parent.parent
sys.path.insert(0, str(server_dir))


def run_unit_tests():
    """Run unit tests."""
    print("=" * 60)
    print("RUNNING UNIT TESTS")
    print("=" * 60)
    
    # Run unit tests (if they exist)
    unit_test_dir = Path(__file__).parent / "unit"
    if unit_test_dir.exists():
        result = pytest.main([
            str(unit_test_dir),
            "-v",
            "--tb=short",
            "--durations=10"
        ])
    else:
        print("No unit tests found - skipping")
        result = 0
    
    return result


def run_integration_tests():
    """Run integration tests."""
    print("\n" + "=" * 60)
    print("RUNNING INTEGRATION TESTS")
    print("=" * 60)
    
    integration_test_dir = Path(__file__).parent / "integration"
    result = pytest.main([
        str(integration_test_dir),
        "-v",
        "--tb=short",
        "--durations=10"
    ])
    
    return result


def run_performance_tests():
    """Run performance benchmarks."""
    print("\n" + "=" * 60)
    print("RUNNING PERFORMANCE TESTS")
    print("=" * 60)
    
    # Run performance tests with specific markers
    result = pytest.main([
        str(Path(__file__).parent),
        "-v",
        "-m", "performance",
        "--tb=short"
    ])
    
    return result


def run_cache_tests():
    """Run cache-specific tests."""
    print("\n" + "=" * 60)
    print("RUNNING CACHE TESTS")
    print("=" * 60)
    
    cache_test_file = Path(__file__).parent / "integration" / "test_cache_integration.py"
    result = pytest.main([
        str(cache_test_file),
        "-v",
        "--tb=short"
    ])
    
    return result


def run_analytics_tests():
    """Run analytics-specific tests."""
    print("\n" + "=" * 60)
    print("RUNNING ANALYTICS TESTS")
    print("=" * 60)
    
    analytics_test_file = Path(__file__).parent / "integration" / "test_analytics_pipeline.py"
    result = pytest.main([
        str(analytics_test_file),
        "-v",
        "--tb=short"
    ])
    
    return result


def run_data_pipeline_tests():
    """Run data pipeline tests."""
    print("\n" + "=" * 60)
    print("RUNNING DATA PIPELINE TESTS")
    print("=" * 60)
    
    pipeline_test_file = Path(__file__).parent / "integration" / "test_data_pipeline.py"
    result = pytest.main([
        str(pipeline_test_file),
        "-v",
        "--tb=short"
    ])
    
    return result


def run_all_tests():
    """Run all tests."""
    print("StockPal Test Suite")
    print("=" * 60)
    
    start_time = time.time()
    results = []
    
    # Run different test categories
    test_categories = [
        ("Unit Tests", run_unit_tests),
        ("Data Pipeline Tests", run_data_pipeline_tests),
        ("Cache Tests", run_cache_tests),
        ("Analytics Tests", run_analytics_tests),
        ("Integration Tests", run_integration_tests),
    ]
    
    for category_name, test_function in test_categories:
        print(f"\n{'='*20} {category_name} {'='*20}")
        category_start = time.time()
        
        try:
            result = test_function()
            category_time = time.time() - category_start
            results.append((category_name, result, category_time))
            
            if result == 0:
                print(f"✅ {category_name} PASSED ({category_time:.2f}s)")
            else:
                print(f"❌ {category_name} FAILED ({category_time:.2f}s)")
                
        except Exception as e:
            category_time = time.time() - category_start
            results.append((category_name, -1, category_time))
            print(f"💥 {category_name} ERROR: {str(e)} ({category_time:.2f}s)")
    
    # Print summary
    total_time = time.time() - start_time
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    failed = 0
    errors = 0
    
    for category_name, result, category_time in results:
        status_icon = "✅" if result == 0 else "❌" if result > 0 else "💥"
        print(f"{status_icon} {category_name:<25} ({category_time:>6.2f}s)")
        
        if result == 0:
            passed += 1
        elif result > 0:
            failed += 1
        else:
            errors += 1
    
    print("-" * 60)
    print(f"Total: {len(results)} categories")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Errors: {errors}")
    print(f"Total time: {total_time:.2f}s")
    
    # Return overall result
    if failed > 0 or errors > 0:
        print("\n❌ OVERALL RESULT: FAILED")
        return 1
    else:
        print("\n✅ OVERALL RESULT: PASSED")
        return 0


def run_quick_tests():
    """Run a quick subset of tests for development."""
    print("StockPal Quick Test Suite")
    print("=" * 60)
    
    # Run only essential tests
    result = pytest.main([
        str(Path(__file__).parent / "integration"),
        "-v",
        "--tb=short",
        "-x",  # Stop on first failure
        "--durations=5"
    ])
    
    return result


def run_coverage_tests():
    """Run tests with coverage reporting."""
    print("StockPal Test Suite with Coverage")
    print("=" * 60)
    
    result = pytest.main([
        str(Path(__file__).parent),
        "--cov=server",
        "--cov-report=html",
        "--cov-report=term-missing",
        "-v"
    ])
    
    return result


def main():
    """Main test runner."""
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "quick":
            return run_quick_tests()
        elif command == "coverage":
            return run_coverage_tests()
        elif command == "unit":
            return run_unit_tests()
        elif command == "integration":
            return run_integration_tests()
        elif command == "cache":
            return run_cache_tests()
        elif command == "analytics":
            return run_analytics_tests()
        elif command == "pipeline":
            return run_data_pipeline_tests()
        elif command == "performance":
            return run_performance_tests()
        elif command == "all":
            return run_all_tests()
        else:
            print(f"Unknown command: {command}")
            print("Available commands: quick, coverage, unit, integration, cache, analytics, pipeline, performance, all")
            return 1
    else:
        # Default to running all tests
        return run_all_tests()


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
