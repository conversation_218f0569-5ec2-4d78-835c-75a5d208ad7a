"""
Integration tests for the complete data pipeline.

These tests validate the entire data flow from external sources through
repositories to analysis services.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from shared.models.stock_models import DataProvider, AnalysisRequest
from shared.exceptions.stock_exceptions import (
    DataFetchException, SymbolNotFoundException, InsufficientDataException
)
from application.use_cases.data_fetching_use_case import DataFetchingUseCase
from application.use_cases.stock_analysis_use_case import StockAnalysisUseCase


class TestDataPipeline:
    """Test the complete data pipeline integration."""
    
    def test_fetch_and_store_daily_prices(self, data_service, test_symbols):
        """Test fetching and storing daily price data."""
        symbol = test_symbols[0]
        
        # Mock the external data fetcher to return test data
        with patch.object(data_service._data_fetcher, 'fetch_daily_prices') as mock_fetch:
            # Setup mock to return sample data
            mock_fetch.return_value = self._create_mock_prices(symbol, 30)
            
            # Fetch data
            prices = data_service.get_daily_prices(symbol=symbol, days=30, force_refresh=True)
            
            # Validate results
            assert len(prices) == 30
            assert all(p.symbol == symbol for p in prices)
            assert all(p.close_price > 0 for p in prices)
            
            # Verify data was stored in repository
            stored_prices = data_service._price_repository.get_daily_prices(symbol, 30)
            assert len(stored_prices) == 30
    
    def test_fetch_from_multiple_providers(self, data_service, test_providers):
        """Test fetching data from multiple providers."""
        symbol = "VIC"
        
        for provider in test_providers:
            with patch.object(data_service._data_fetcher, 'fetch_daily_prices') as mock_fetch:
                mock_fetch.return_value = self._create_mock_prices(symbol, 20)
                
                # Fetch data from specific provider
                prices = data_service.get_daily_prices(
                    symbol=symbol, 
                    days=20, 
                    provider=provider,
                    force_refresh=True
                )
                
                assert len(prices) == 20
                assert mock_fetch.called
    
    def test_data_validation_and_error_handling(self, data_service):
        """Test data validation and error handling in the pipeline."""
        
        # Test invalid symbol
        with pytest.raises(SymbolNotFoundException):
            data_service.get_daily_prices(symbol="INVALID_SYMBOL", days=30)
        
        # Test insufficient data
        with patch.object(data_service._data_fetcher, 'fetch_daily_prices') as mock_fetch:
            mock_fetch.return_value = []  # No data returned
            
            with pytest.raises(InsufficientDataException):
                data_service.get_daily_prices(symbol="VIC", days=30, force_refresh=True)
    
    def test_cache_integration(self, data_service, cache_manager):
        """Test cache integration in the data pipeline."""
        symbol = "VNM"
        
        # Inject cache manager into data service
        data_service._cache_manager = cache_manager
        
        with patch.object(data_service._data_fetcher, 'fetch_daily_prices') as mock_fetch:
            mock_prices = self._create_mock_prices(symbol, 25)
            mock_fetch.return_value = mock_prices
            
            # First fetch - should hit external source
            prices1 = data_service.get_daily_prices(symbol=symbol, days=25, force_refresh=True)
            assert mock_fetch.call_count == 1
            
            # Cache the data
            cache_manager.set_daily_prices(symbol, 25, "ssi", prices1)
            
            # Second fetch - should hit cache
            cached_prices = cache_manager.get_daily_prices(symbol, 25, "ssi")
            assert cached_prices is not None
            assert len(cached_prices) == 25
    
    def test_end_to_end_analysis_pipeline(self, data_service, analysis_service):
        """Test the complete end-to-end analysis pipeline."""
        symbol = "HPG"
        
        with patch.object(data_service._data_fetcher, 'fetch_daily_prices') as mock_fetch:
            # Create realistic price data for analysis
            mock_prices = self._create_trending_prices(symbol, 60)
            mock_fetch.return_value = mock_prices
            
            # Create analysis request
            request = AnalysisRequest(
                symbol=symbol,
                days_back=60,
                end_date=datetime.now(),
                indicators=[]  # Use default indicators
            )
            
            # Perform complete analysis
            analysis = analysis_service.analyze_stock(request)
            
            # Validate analysis results
            assert analysis.symbol == symbol
            assert analysis.current_price > 0
            assert analysis.technical_indicators is not None
            assert len(analysis.technical_indicators) > 0
            assert analysis.trend_analysis is not None
            assert analysis.confidence_score >= 0
    
    def test_concurrent_data_fetching(self, data_service, test_symbols):
        """Test concurrent data fetching for multiple symbols."""
        import concurrent.futures
        
        def fetch_symbol_data(symbol):
            with patch.object(data_service._data_fetcher, 'fetch_daily_prices') as mock_fetch:
                mock_fetch.return_value = self._create_mock_prices(symbol, 30)
                return data_service.get_daily_prices(symbol=symbol, days=30, force_refresh=True)
        
        # Fetch data for multiple symbols concurrently
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            futures = {
                executor.submit(fetch_symbol_data, symbol): symbol 
                for symbol in test_symbols[:3]
            }
            
            results = {}
            for future in concurrent.futures.as_completed(futures):
                symbol = futures[future]
                try:
                    prices = future.result()
                    results[symbol] = prices
                except Exception as e:
                    pytest.fail(f"Failed to fetch data for {symbol}: {e}")
        
        # Validate all symbols were processed
        assert len(results) == 3
        for symbol, prices in results.items():
            assert len(prices) == 30
            assert all(p.symbol == symbol for p in prices)
    
    def test_data_consistency_across_services(self, data_service, analysis_service):
        """Test data consistency across different services."""
        symbol = "TCB"
        
        with patch.object(data_service._data_fetcher, 'fetch_daily_prices') as mock_fetch:
            mock_prices = self._create_mock_prices(symbol, 40)
            mock_fetch.return_value = mock_prices
            
            # Fetch data through data service
            data_service_prices = data_service.get_daily_prices(symbol=symbol, days=40, force_refresh=True)
            
            # Create analysis request
            request = AnalysisRequest(symbol=symbol, days_back=40)
            
            # Get data through analysis service
            analysis = analysis_service.analyze_stock(request)
            
            # Verify consistency
            assert len(data_service_prices) == 40
            assert analysis.symbol == symbol
            assert analysis.current_price == data_service_prices[-1].close_price
    
    def test_error_propagation_through_pipeline(self, data_service, analysis_service):
        """Test error propagation through the entire pipeline."""
        symbol = "ERROR_TEST"
        
        # Test network error propagation
        with patch.object(data_service._data_fetcher, 'fetch_daily_prices') as mock_fetch:
            mock_fetch.side_effect = DataFetchException("Network error")
            
            with pytest.raises(DataFetchException):
                data_service.get_daily_prices(symbol=symbol, days=30, force_refresh=True)
        
        # Test analysis with insufficient data
        with patch.object(data_service._data_fetcher, 'fetch_daily_prices') as mock_fetch:
            mock_fetch.return_value = self._create_mock_prices(symbol, 5)  # Too little data
            
            request = AnalysisRequest(symbol=symbol, days_back=30)
            
            with pytest.raises(InsufficientDataException):
                analysis_service.analyze_stock(request)
    
    # Helper methods
    
    def _create_mock_prices(self, symbol, count):
        """Create mock price data for testing."""
        from tests.conftest import PricePoint
        
        prices = []
        base_price = 100.0
        base_timestamp = int(datetime.now().timestamp()) - (count * 24 * 3600)
        
        for i in range(count):
            price_change = (i % 10 - 5) * 0.01
            current_price = base_price * (1 + price_change)
            
            price_point = PricePoint(
                timestamp=base_timestamp + (i * 24 * 3600),
                open_price=current_price * 0.99,
                high_price=current_price * 1.02,
                low_price=current_price * 0.98,
                close_price=current_price,
                volume=1000000 + (i * 10000),
                change_price=current_price - base_price,
                change_percent=((current_price - base_price) / base_price) * 100,
                symbol=symbol
            )
            prices.append(price_point)
            base_price = current_price
        
        return prices
    
    def _create_trending_prices(self, symbol, count):
        """Create trending price data for analysis testing."""
        from tests.conftest import PricePoint
        
        prices = []
        base_price = 80.0
        base_timestamp = int(datetime.now().timestamp()) - (count * 24 * 3600)
        
        for i in range(count):
            # Create uptrend with some volatility
            trend_change = 0.008  # 0.8% daily trend
            volatility = (i % 6 - 3) * 0.003  # Random volatility
            total_change = trend_change + volatility
            current_price = base_price * (1 + total_change)
            
            price_point = PricePoint(
                timestamp=base_timestamp + (i * 24 * 3600),
                open_price=current_price * 0.995,
                high_price=current_price * 1.015,
                low_price=current_price * 0.985,
                close_price=current_price,
                volume=800000 + (i * 8000),
                change_price=current_price - base_price,
                change_percent=((current_price - base_price) / base_price) * 100,
                symbol=symbol
            )
            prices.append(price_point)
            base_price = current_price
        
        return prices
