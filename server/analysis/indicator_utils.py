"""
indicator_utils.py

Utility functions for calculating and aggregating technical indicator values for stocks.
This module is intended to be imported by analysis and reporting scripts to provide
consistent, reusable indicator calculation logic.
"""

import logging
from datetime import datetime
from typing import Any, Dict, List

import numpy as np
from stockpal.core.constants import Constants
from stockpal.core.stock import PriceData
from stockpal.data.data_reader import DataReader
from stockpal.indicator.adi import AccumulationDistributionIndex
from stockpal.indicator.adx import AverageDirectionalIndex
from stockpal.indicator.ao import AwesomeOscillator
from stockpal.indicator.bb import BollingerBands
from stockpal.indicator.bearpower import BearPower
from stockpal.indicator.cci import CommodityChannelIndex
from stockpal.indicator.ichimoku import Ichimoku
from stockpal.indicator.ma import MovingAverage
from stockpal.indicator.macd import MACD
from stockpal.indicator.momentum import Momentum
from stockpal.indicator.obv import OnBalanceVolume
from stockpal.indicator.pivot_points import PivotPoints
from stockpal.indicator.roc import RateOfChange
from stockpal.indicator.rs import RelativeStrength
from stockpal.indicator.rsi import RelativeStrengthIndex
from stockpal.indicator.sar import ParabolicSAR
from stockpal.indicator.signal_synthesizer import SignalSynthesizer
from stockpal.indicator.stoch import StochasticOscillator
from stockpal.indicator.stochrsi import StochasticRSI
from stockpal.indicator.tsf import TimeSeriesForecast
from stockpal.indicator.ultosc import UltimateOscillator
from stockpal.indicator.wpr import WilliamsPercentR
from analysis.formatting_utils import _indicator_signal_and_meta
from analysis.formatting_utils import generate_ma_signals_table

# ... (move all relevant functions here, with correct imports and type hints) ...


def get_last_trading_weekdays(prices: list[PriceData], n: int) -> list[int]:
    # Group by ISO week
    week_map: dict[tuple[int, int], PriceData] = {}
    for p in prices:
        dt = datetime.fromtimestamp(p.timestamp)
        year_week = (dt.isocalendar().year, dt.isocalendar().week)
        if year_week not in week_map or p.timestamp > week_map[year_week].timestamp:
            week_map[year_week] = p
    trading_week_days = sorted(
        week_map.values(), key=lambda x: x.timestamp, reverse=True
    )
    return [p.timestamp for p in trading_week_days[-n:]]


def get_prices_up_to(prices: list[PriceData], timestamp: int) -> list[PriceData]:
    """Return all prices up to and including the given timestamp (assumes sorted by timestamp in descending order)."""
    return [p for p in prices if p.timestamp <= timestamp]


def _calculate_ma_arrays(
    symbol: str, prices: list[PriceData], trading_week_days: list[int]
) -> dict:
    ma_periods = [5, 10, 20, 50, 100, 200]
    ma_arrays = {"t": trading_week_days}
    for period in ma_periods:
        ma_arrays[f"sma{period}"] = []
        ma_arrays[f"ema{period}"] = []
    for ts in trading_week_days:
        sub_prices = get_prices_up_to(prices, ts)
        for period in ma_periods:
            sma_calc = MovingAverage(
                symbol, sub_prices, period=period, ma_type="simple"
            )
            sma_values = sma_calc.calculate()
            ma_arrays[f"sma{period}"].append(sma_values[-1] if sma_values else None)
            ema_calc = MovingAverage(
                symbol, sub_prices, period=period, ma_type="exponential"
            )
            ema_values = ema_calc.calculate()
            ma_arrays[f"ema{period}"].append(ema_values[-1] if ema_values else None)
    return ma_arrays


def _calculate_macd_arrays(
    symbol: str, prices: list[PriceData], trading_week_days: list[int]
) -> dict:
    macd_arrays = {"t": [], "macd_line": [], "signal_line": [], "histogram": []}
    for ts in trading_week_days:
        sub_prices = get_prices_up_to(prices, ts)
        macd_sub = MACD(symbol, sub_prices)
        macd_result = macd_sub.calculate()
        macd_arrays["t"].append(ts)
        macd_arrays["macd_line"].append(
            macd_result["macd_line"][-1] if macd_result["macd_line"] else None
        )
        macd_arrays["signal_line"].append(
            macd_result["signal_line"][-1] if macd_result["signal_line"] else None
        )
        macd_arrays["histogram"].append(
            macd_result["histogram"][-1] if macd_result["histogram"] else None
        )
    return macd_arrays


def _calculate_rsi_arrays(
    symbol: str, prices: list[PriceData], trading_week_days: list[int]
) -> dict:
    rsi_arrays = {"t": [], "v": []}
    for ts in trading_week_days:
        sub_prices = get_prices_up_to(prices, ts)
        rsi_sub = RelativeStrengthIndex(symbol, sub_prices)
        rsi_values = rsi_sub.calculate()
        rsi_arrays["t"].append(ts)
        rsi_arrays["v"].append(rsi_values[-1] if rsi_values else None)
    return rsi_arrays


def _calculate_rsw_arrays(
    symbol: str, prices: list[PriceData], trading_week_days: list[int]
) -> dict:
    rsw_arrays = {"t": [], "v": []}
    try:
        benchmark_prices = DataReader("VNINDEX").get_daily_prices(days=365)
        for ts in trading_week_days:
            sub_prices = get_prices_up_to(prices, ts)
            sub_bench = get_prices_up_to(benchmark_prices, ts)
            rsw = RelativeStrength(
                symbol=symbol, prices=sub_prices, benchmark_prices=sub_bench
            )
            rsw_values = rsw.calculate()
            rsw_arrays["t"].append(ts)
            rsw_arrays["v"].append(rsw_values[-1] if rsw_values else None)
    except Exception as e:
        logging.warning(f"Could not calculate RelativeStrength vs VNINDEX: {e}")
        for ts in trading_week_days:
            rsw_arrays["t"].append(ts)
            rsw_arrays["v"].append(None)
    return rsw_arrays


def _calculate_adx_arrays(
    symbol: str, prices: list[PriceData], trading_week_days: list[int]
) -> dict:
    adx_arrays = {"t": [], "a": [], "p": [], "m": []}
    for ts in trading_week_days:
        sub_prices = get_prices_up_to(prices, ts)
        adx = AverageDirectionalIndex(symbol, sub_prices)
        vals = adx.calculate()
        adx_arrays["t"].append(ts)
        adx_arrays["a"].append(vals["adx"][-1] if vals["adx"] else None)
        adx_arrays["p"].append(vals["plus_di"][-1] if vals["plus_di"] else None)
        adx_arrays["m"].append(vals["minus_di"][-1] if vals["minus_di"] else None)
    return adx_arrays


def _calculate_ichimoku_arrays(
    symbol: str, prices: list[PriceData], trading_week_days: list[int]
) -> dict:
    ichimoku_arrays = {"t": [], "tk": [], "kj": [], "sa": [], "sb": [], "cs": []}
    for ts in trading_week_days:
        sub_prices = get_prices_up_to(prices, ts)
        ichimoku = Ichimoku(symbol, sub_prices)
        vals = ichimoku.calculate()
        ichimoku_arrays["t"].append(ts)
        ichimoku_arrays["tk"].append(
            vals["tenkan_sen"][-1] if vals["tenkan_sen"] else None
        )
        ichimoku_arrays["kj"].append(
            vals["kijun_sen"][-1] if vals["kijun_sen"] else None
        )
        ichimoku_arrays["sa"].append(
            vals["senkou_span_a"][-1] if vals["senkou_span_a"] else None
        )
        ichimoku_arrays["sb"].append(
            vals["senkou_span_b"][-1] if vals["senkou_span_b"] else None
        )
        ichimoku_arrays["cs"].append(
            vals["chikou_span"][-1] if vals["chikou_span"] else None
        )
    return ichimoku_arrays


def _calculate_pp_arrays(
    symbol: str, prices: list[PriceData], trading_week_days: list[int]
) -> dict:
    pp_arrays = {
        "t": [],
        "pivot": [],
        "r1": [],
        "r2": [],
        "r3": [],
        "s1": [],
        "s2": [],
        "s3": [],
    }
    for ts in trading_week_days:
        sub_prices = get_prices_up_to(prices, ts)
        pp = PivotPoints(symbol, sub_prices)
        vals = pp.calculate()
        pp_arrays["t"].append(ts)
        pp_arrays["pivot"].append(vals.get("PP"))
        pp_arrays["r1"].append(vals.get("R1"))
        pp_arrays["r2"].append(vals.get("R2"))
        pp_arrays["r3"].append(vals.get("R3"))
        pp_arrays["s1"].append(vals.get("S1"))
        pp_arrays["s2"].append(vals.get("S2"))
        pp_arrays["s3"].append(vals.get("S3"))
    return pp_arrays


def _calculate_tsf(
    symbol: str, prices: list[PriceData]
) -> tuple[list[float], list[dict], float | None, str]:
    tsf_calc = TimeSeriesForecast(symbol, prices, period=14)
    tsf_values = tsf_calc.calculate()
    tsf_signals = tsf_calc.get_signals()
    current_tsf = tsf_values[-1] if tsf_values and tsf_values[-1] is not None else None
    tsf_signal = "Trung tính"
    if tsf_signals:
        last_signal = tsf_signals[-1]
        if last_signal["signal"] == "bullish":
            tsf_signal = "Mua"
        elif last_signal["signal"] == "bearish":
            tsf_signal = "Bán"
    return tsf_values, tsf_signals, current_tsf, tsf_signal


def _calculate_bb(symbol: str, prices: list[PriceData]) -> tuple[list[float], float]:
    bb_calc = BollingerBands(symbol, prices, period=20, num_std_dev=2.0)
    bb_widths = bb_calc.get_band_width()
    bb_width = bb_widths[-1] if bb_widths and bb_widths[-1] is not None else 0.0
    return bb_widths, bb_width


def _calculate_stochastic(
    symbol: str, prices: list[PriceData]
) -> tuple[float | None, float | None]:
    stoch = StochasticOscillator(symbol, prices)
    stoch_result = stoch.calculate()
    stoch_k = None
    stoch_d = None
    if isinstance(stoch_result, dict):
        if "k_values" in stoch_result and stoch_result["k_values"]:
            stoch_k = next(
                (v for v in reversed(stoch_result["k_values"]) if v is not None), None
            )
        if "d_values" in stoch_result and stoch_result["d_values"]:
            stoch_d = next(
                (v for v in reversed(stoch_result["d_values"]) if v is not None), None
            )
    return stoch_k, stoch_d


def _calculate_cci(symbol: str, prices: list[PriceData]) -> float | None:
    cci = CommodityChannelIndex(symbol, prices)
    cci_values = cci.calculate()
    return (
        next((v for v in reversed(cci_values) if v is not None), None)
        if isinstance(cci_values, list) and cci_values
        else None
    )


def _calculate_adx_values(
    symbol: str, prices: list[PriceData]
) -> tuple[float | None, float | None, float | None]:
    adx = AverageDirectionalIndex(symbol, prices)
    adx_result = adx.calculate()
    adx_val = plus_di = minus_di = None
    if isinstance(adx_result, dict):
        if (
            "adx" in adx_result
            and isinstance(adx_result["adx"], list)
            and adx_result["adx"]
        ):
            adx_val = next(
                (v for v in reversed(adx_result["adx"]) if v is not None), None
            )
        if (
            "plus_di" in adx_result
            and isinstance(adx_result["plus_di"], list)
            and adx_result["plus_di"]
        ):
            plus_di = next(
                (v for v in reversed(adx_result["plus_di"]) if v is not None), None
            )
        if (
            "minus_di" in adx_result
            and isinstance(adx_result["minus_di"], list)
            and adx_result["minus_di"]
        ):
            minus_di = next(
                (v for v in reversed(adx_result["minus_di"]) if v is not None), None
            )
    return adx_val, plus_di, minus_di


def _calculate_wpr(symbol: str, prices: list[PriceData]) -> float | None:
    wpr = WilliamsPercentR(symbol, prices)
    wpr_values = wpr.calculate()
    return (
        next((v for v in reversed(wpr_values) if v is not None), None)
        if isinstance(wpr_values, list) and wpr_values
        else None
    )


def _calculate_roc(symbol: str, prices: list[PriceData]) -> float | None:
    roc = RateOfChange(symbol, prices)
    roc_values = roc.calculate()
    return (
        next((v for v in reversed(roc_values) if v is not None), None)
        if isinstance(roc_values, list) and roc_values
        else None
    )


def _calculate_ultosc(symbol: str, prices: list[PriceData]) -> float | None:
    ultosc = UltimateOscillator(symbol, prices)
    ultosc_values = ultosc.calculate()
    return (
        next((v for v in reversed(ultosc_values) if v is not None), None)
        if isinstance(ultosc_values, list) and ultosc_values
        else None
    )


def _calculate_sar(symbol: str, prices: list[PriceData]) -> float | None:
    sar = ParabolicSAR(symbol, prices)
    sar_values = sar.calculate()
    return (
        next((v for v in reversed(sar_values) if v is not None), None)
        if isinstance(sar_values, list) and sar_values
        else None
    )


def _calculate_obv(symbol: str, prices: list[PriceData]) -> float | None:
    obv_indicator = OnBalanceVolume(symbol, prices)
    obv_values = obv_indicator.calculate()
    return obv_values[-1] if obv_values and obv_values[-1] is not None else None


def _calculate_ao(symbol: str, prices: list[PriceData]) -> float | None:
    ao = AwesomeOscillator(symbol, prices)
    ao_values = ao.calculate()
    return ao_values[-1] if ao_values and ao_values[-1] is not None else None


def _calculate_adi(symbol: str, prices: list[PriceData]) -> float | None:
    adi_indicator = AccumulationDistributionIndex(symbol, prices)
    adi_values = adi_indicator.calculate()
    return (
        adi_values["adi"][-1]
        if adi_values
        and "adi" in adi_values
        and adi_values["adi"]
        and adi_values["adi"][-1] is not None
        else None
    )


def _calculate_momentum(symbol: str, prices: list[PriceData]) -> float | None:
    momentum = Momentum(symbol, prices)
    mom_values = momentum.calculate()
    return mom_values[-1] if mom_values and mom_values[-1] is not None else None


def _calculate_bear_power(symbol: str, prices: list[PriceData]) -> float | None:
    bear_power = BearPower(symbol, prices)
    bear_values = bear_power.calculate()
    return bear_values[-1] if bear_values and bear_values[-1] is not None else None


def _calculate_volume_spike(prices: list[PriceData]) -> tuple[float, bool]:
    volumes = [p.match_volume for p in prices[-20:]]
    avg_volume = sum(volumes) / len(volumes) if volumes else 0
    volume_spike = (
        any(v > 1.5 * avg_volume for v in volumes[-5:]) if avg_volume else False
    )
    return avg_volume, volume_spike


def calculate_indicators(symbol: str, prices: list[PriceData]) -> dict[str, Any]:
    trading_week_days = get_last_trading_weekdays(prices, 5)
    logging.debug(f"Trading days: {trading_week_days}")

    ma_arrays = _calculate_ma_arrays(symbol, prices, trading_week_days)
    macd_arrays = _calculate_macd_arrays(symbol, prices, trading_week_days)
    rsi_arrays = _calculate_rsi_arrays(symbol, prices, trading_week_days)
    rsw_arrays = _calculate_rsw_arrays(symbol, prices, trading_week_days)
    adx_arrays = _calculate_adx_arrays(symbol, prices, trading_week_days)
    ichimoku_arrays = _calculate_ichimoku_arrays(symbol, prices, trading_week_days)
    pp_arrays = _calculate_pp_arrays(symbol, prices, trading_week_days)
    tsf_values, tsf_signals, current_tsf, tsf_signal = _calculate_tsf(symbol, prices)
    bb_widths, bb_width = _calculate_bb(symbol, prices)
    stoch_k, stoch_d = _calculate_stochastic(symbol, prices)
    cci_val = _calculate_cci(symbol, prices)
    adx_val, plus_di, minus_di = _calculate_adx_values(symbol, prices)
    wpr_val = _calculate_wpr(symbol, prices)
    roc_val = _calculate_roc(symbol, prices)
    ultosc_val = _calculate_ultosc(symbol, prices)
    sar_val = _calculate_sar(symbol, prices)
    obv_val = _calculate_obv(symbol, prices)
    ao_val = _calculate_ao(symbol, prices)
    adi_val = _calculate_adi(symbol, prices)
    mom_val = _calculate_momentum(symbol, prices)
    bear_val = _calculate_bear_power(symbol, prices)
    avg_volume, volume_spike = _calculate_volume_spike(prices)

    current_price = prices[-1].close_price
    trend_change = (
        prices[-1].change_price if hasattr(prices[-1], "change_price") else 0.0
    )
    trend_change_percent = (
        prices[-1].change_price_percent
        if hasattr(prices[-1], "change_price_percent")
        else 0.0
    )
    ma_latest = {
        k: (v[-1] if isinstance(v, list) and v else v)
        for k, v in ma_arrays.items()
        if k != "t"
    }
    ma_signals = generate_ma_signals_table(current_price, ma_latest)
    sma5_calc = MovingAverage(symbol, prices, period=5, ma_type="simple")
    sma5_values = sma5_calc.calculate()
    sma5 = sma5_values[-1] if sma5_values and sma5_values[-1] is not None else 0.0
    sma20_calc = MovingAverage(symbol, prices, period=20, ma_type="simple")
    sma20_values = sma20_calc.calculate()
    sma20 = sma20_values[-1] if sma20_values and sma20_values[-1] is not None else 0.0
    macd_indicator = MACD(symbol, prices)
    macd_result = macd_indicator.calculate()
    macd_hist = macd_result["histogram"][-1] if macd_result["histogram"] else 0.0
    macd_line = macd_result["macd_line"][-1] if macd_result["macd_line"] else 0.0
    macd_signal = macd_result["signal_line"][-1] if macd_result["signal_line"] else 0.0

    # Use new interface for RSI
    rsi_indicator = RelativeStrengthIndex(symbol, prices)
    rsi_arrays = {"t": [], "v": []}
    for ts in trading_week_days:
        sub_prices = get_prices_up_to(prices, ts)
        rsi_sub = RelativeStrengthIndex(symbol, sub_prices)
        rsi_values = rsi_sub.calculate()
        rsi_arrays["t"].append(ts)
        rsi_arrays["v"].append(rsi_values[-1] if rsi_values else None)

    # Use new interface for MACD
    macd_arrays = {"t": [], "macd_line": [], "signal_line": [], "histogram": []}
    for ts in trading_week_days:
        sub_prices = get_prices_up_to(prices, ts)
        macd_sub = MACD(symbol, sub_prices)
        macd_result = macd_sub.calculate()
        macd_arrays["t"].append(ts)
        macd_arrays["macd_line"].append(
            macd_result["macd_line"][-1] if macd_result["macd_line"] else None
        )
        macd_arrays["signal_line"].append(
            macd_result["signal_line"][-1] if macd_result["signal_line"] else None
        )
        macd_arrays["histogram"].append(
            macd_result["histogram"][-1] if macd_result["histogram"] else None
        )

    # Use new interface for current values
    rsi = rsi_indicator.calculate()[-1] if rsi_indicator.calculate() else 50.0
    macd_result = macd_indicator.calculate()
    macd_hist = macd_result["histogram"][-1] if macd_result["histogram"] else 0.0
    macd_line = macd_result["macd_line"][-1] if macd_result["macd_line"] else 0.0
    macd_signal = macd_result["signal_line"][-1] if macd_result["signal_line"] else 0.0

    # Compose indicator table
    indicator_table = []
    for k, v in [
        ("RS(52W)", None),
        ("RSI", rsi_arrays["v"][-1] if rsi_arrays["v"] else None),
        ("MACD", macd_line),
        ("MACD Histogram", macd_hist),
        ("STOCHRSI_fastk", None),
        ("CCI", cci_val),
        ("WPR", wpr_val),
        ("ULTOSC", ultosc_val),
        ("ROC", roc_val),
        ("SAR", sar_val),
        ("OBV", obv_val),
        ("AO", ao_val),
        ("ADI", adi_val),
        ("BB", bb_width),
        ("Momentum", mom_val),
        ("Bear Power", bear_val),
        ("Stochastic", stoch_k),
    ]:
        indicator_table.append(_indicator_signal_and_meta(k, v))

    adx_entry = {
        "name": "ADX / +DI / -DI",
        "value": f"{adx_val:.2f} / {plus_di:.2f} / {minus_di:.2f}",
        "signal": (
            "Tăng mạnh"
            if adx_val and adx_val > 25 and plus_di and minus_di and plus_di > minus_di
            else (
                "Giảm mạnh"
                if adx_val
                and adx_val > 25
                and plus_di
                and minus_di
                and plus_di < minus_di
                else "Sideway"
            )
        ),
        "action": (
            "Xu hướng tăng mạnh, ưu tiên mua"
            if adx_val and adx_val > 25 and plus_di and minus_di and plus_di > minus_di
            else (
                "Xu hướng giảm mạnh, ưu tiên bán"
                if adx_val
                and adx_val > 25
                and plus_di
                and minus_di
                and plus_di < minus_di
                else "Thị trường sideway, quan sát thêm"
            )
        ),
        "description": "ADX đo sức mạnh xu hướng, +DI/-DI xác định hướng. ADX > 25 là xu hướng mạnh.",
        "info": "ADX đo độ mạnh của xu hướng, +DI > -DI là tăng, -DI > +DI là giảm. Kết hợp 3 chỉ báo để xác nhận tín hiệu giao dịch theo xu hướng.",
    }
    indicator_table.insert(3, adx_entry)

    synthesizer = SignalSynthesizer(symbol=symbol, prices=prices, language="vi")
    analysis = synthesizer.analyze()
    return {
        "current_price": current_price,
        "trend_change": trend_change,
        "trend_change_percent": trend_change_percent,
        "ma_signals": ma_signals,
        "sma5": sma5,
        "sma20": sma20,
        "macd_hist": macd_hist,
        "macd_line": macd_line,
        "macd_signal": macd_signal,
        "rsi": rsi,
        "tsf": current_tsf,
        "tsf_signal": tsf_signal,
        "bb_width": bb_width,
        "avg_volume": avg_volume,
        "volume_spike": volume_spike,
        "buy_zones": analysis.get("buy_zones", []),
        "stop_loss_zones": analysis.get("stop_loss_zones", []),
        "take_profit_zones": analysis.get("take_profit_zones", []),
        "risk_reward_ratios": analysis.get("risk_reward_ratios", []),
        "recommendation": analysis.get("recommendation", "Không có khuyến nghị"),
        "trend_direction": analysis.get("trend_direction", "Đi ngang"),
        "trend_strength": analysis.get("trend_strength", "Trung bình"),
        "trend_confidence": f"{analysis.get('trend_confidence', 0):.2f}%",
        "market_condition": analysis.get("market_condition", "Trung lập"),
        "last_trading_date": datetime.fromtimestamp(prices[-1].timestamp).strftime(
            "%Y-%m-%d"
        ),
        "enhanced_signals": indicator_table,
        "indicator_arrays": {
            "ma": ma_arrays,
            "macd": macd_arrays,
            "rsi": rsi_arrays,
            "rsw": rsw_arrays,
            "adx": adx_arrays,
            "ichimoku": ichimoku_arrays,
            "pivot_points": pp_arrays,
        },
    }


# ... (repeat for all other indicator calculation and aggregation functions) ...
