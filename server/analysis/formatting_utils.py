"""
formatting_utils.py

Utility functions for formatting, mapping, and exporting indicator and analysis data
for web, reporting, and downstream consumption. Intended for use by analysis scripts
and web export pipelines.
"""
import logging
from datetime import datetime
from typing import Any, Dict, List
from stockpal.core.stock import PriceData

def _indicator_signal_and_meta(name: str, value: float | None) -> dict:
    """
    Trả về dict gồm: name, value, signal, action, description, info cho từng chỉ báo kỹ thuật.
    """
    if value is None:
        return {
            "name": name,
            "value": None,
            "signal": "N/A",
            "action": "Không đủ dữ liệu để đưa ra khuyến nghị.",
            "description": "Không có dữ liệu",
            "info": "Không có thông tin.",
        }
    # Quy tắc tín hiệu, mô tả, hà<PERSON> động, gi<PERSON><PERSON> thích cho từng chỉ báo
    rules = {
        "RS(52W)": {
            "desc": "Relative Strength (52 tuần - 1 năm)",
            "signal": lambda v: "Mạnh" if v > 1 else ("Yếu" if v < 1 else "Trung tính"),
            "action": lambda v: (
                "RS(52W) > 1: <PERSON>ổ phiếu mạnh hơn thị trường, ưu tiên mua."
                if v > 1
                else (
                    "RS(52W) < 1: Cổ phiếu yếu hơn thị trường, ưu tiên tránh."
                    if v < 1
                    else "RS(52W) gần bằng thị trường, chưa có lợi thế vượt trội"
                )
            ),
            "info": "Chỉ báo sức mạnh tương đối so với thị trường (VNINDEX). Giá trị > 1 cho thấy cổ phiếu mạnh hơn thị trường, giá trị < 1 cho thấy yếu hơn. RS(52W) so sánh hiệu suất trong 52 tuần (1 năm), giúp xác định sức mạnh dài hạn của cổ phiếu trong ngành.",
        },
        "RSI": {
            "desc": "Chỉ số sức mạnh tương đối (14 ngày)",
            "signal": lambda v: (
                "Mua" if v < 30 else ("Bán" if v > 70 else "Trung tính")
            ),
            "action": lambda v: (
                "RSI < 30, quá bán, cân nhắc mua vào."
                if v < 30
                else (
                    "RSI > 70, quá mua, cân nhắc bán ra."
                    if v > 70
                    else (
                        "RSI < 50, xu hướng giảm nhẹ, thận trọng với vị thế mua"
                        if v < 50
                        else "RSI trung tính, quan sát thêm."
                    )
                )
            ),
            "info": "RSI đo lường tốc độ và sự thay đổi của biến động giá. Giá trị dưới 30 thường được xem là quá bán (tín hiệu mua), trên 70 là quá mua (tín hiệu bán). Mức 50 là ngưỡng phân cách xu hướng lên/xuống.",
        },
        "MACD": {
            "desc": "Moving Average Convergence Divergence (12,26,9)",
            "signal": lambda v: "Mua" if v > 0 else ("Bán" if v < 0 else "Trung tính"),
            "action": lambda v: (
                "Đường MACD trên đường tín hiệu, xu hướng tăng đang chiếm ưu thế"
                if v > 0
                else (
                    "Đường MACD dưới đường tín hiệu, xu hướng giảm chiếm ưu thế"
                    if v < 0
                    else "MACD trung tính, quan sát thêm."
                )
            ),
            "info": "MACD là sự khác biệt giữa EMA 12 và EMA 26, với đường tín hiệu là EMA 9 của MACD. Khi MACD cắt lên trên đường tín hiệu là dấu hiệu mua, cắt xuống dưới là dấu hiệu bán. Mức 0 là ngưỡng đảo chiều quan trọng.",
        },
        "MACD Histogram": {
            "desc": "MACD Histogram",
            "signal": lambda v: "Mua" if v > 0 else ("Bán" if v < 0 else "Trung tính"),
            "action": lambda v: (
                "Động lượng tăng đang mạnh lên, ưu tiên mua"
                if v > 0
                else (
                    "Động lượng giảm đang mạnh lên, ưu tiên bán"
                    if v < 0
                    else "Động lượng tăng đang chậm lại, cẩn trọng với vị thế mua"
                )
            ),
            "info": "MACD Histogram là sự chênh lệch giữa đường MACD và đường tín hiệu. Giá trị dương và tăng chỉ báo động lượng tăng mạnh, giá trị âm và giảm chỉ báo động lượng giảm mạnh. Khi histogram thay đổi chiều là dấu hiệu đầu tiên của đảo chiều.",
        },
        "STOCHRSI_fastk": {
            "desc": "Stochastic RSI Fast %K (3,3,14,14)",
            "signal": lambda v: (
                "Mua" if v < 20 else ("Bán" if v > 80 else "Trung tính")
            ),
            "action": lambda v: (
                "StochRSI trong vùng quá bán, xem xét mở vị thế mua"
                if v < 20
                else (
                    "StochRSI trong vùng quá mua, xem xét chốt lời/bán"
                    if v > 80
                    else "StochRSI trung tính, quan sát thêm."
                )
            ),
            "info": "Stochastic RSI kết hợp RSI và Stochastic để xác định quá mua/quá bán với độ nhạy cao hơn. Giá trị dưới 20 là quá bán mạnh (cơ hội mua), trên 80 là quá mua mạnh (nên bán). Chỉ báo này có độ nhạy cao hơn RSI thông thường.",
        },
        "ADX": {
            "desc": "Average Directional Index (14)",
            "signal": lambda v: "Mạnh" if v > 25 else "Trung tính",
            "action": lambda v: (
                "ADX > 25: Xu hướng mạnh, có thể giao dịch theo xu hướng."
                if v > 25
                else "Thị trường không có xu hướng rõ ràng, thận trọng khi giao dịch"
            ),
            "info": "ADX đo lường độ mạnh của xu hướng, bất kể hướng tăng hay giảm. Giá trị >25 cho thấy xu hướng mạnh, >40 là xu hướng rất mạnh, <20 là thị trường sideway. +DI và -DI xác định hướng của xu hướng: +DI > -DI là xu hướng tăng, ngược lại là giảm.",
        },
        "CCI": {
            "desc": "Commodity Channel Index (20)",
            "signal": lambda v: (
                "Mua" if v < -100 else ("Bán" if v > 100 else "Trung tính")
            ),
            "action": lambda v: (
                "CCI < -100: Quá bán, cân nhắc mua."
                if v < -100
                else (
                    "CCI > 100: Quá mua, cân nhắc bán."
                    if v > 100
                    else "CCI dương, xu hướng tăng nhẹ"
                )
            ),
            "info": "CCI đo lường sự khác biệt giữa giá hiện tại và giá trung bình lịch sử. Giá trị dưới -100 là quá bán (tín hiệu mua), trên +100 là quá mua (tín hiệu bán). Mức ±200 là vùng cực đoan, thường dẫn đến đảo chiều mạnh.",
        },
        "WPR": {
            "desc": "Williams %R (14)",
            "signal": lambda v: (
                "Mua" if v < -80 else ("Bán" if v > -20 else "Trung tính")
            ),
            "action": lambda v: (
                "Williams %R trong vùng quá bán, cân nhắc mua."
                if v < -80
                else (
                    "Williams %R trong vùng quá mua, cân nhắc bán."
                    if v > -20
                    else "Williams %R trên -50, xu hướng tăng nhẹ"
                )
            ),
            "info": "Williams %R là chỉ báo động lượng đo lường mức độ quá mua hoặc quá bán. Giá trị từ -80 đến -100 là quá bán (tín hiệu mua), từ 0 đến -20 là quá mua (tín hiệu bán). Thường đảo chiều sớm hơn RSI và Stochastic.",
        },
        "ULTOSC": {
            "desc": "Ultimate Oscillator (7,14,28)",
            "signal": lambda v: (
                "Mua" if v < 30 else ("Bán" if v > 70 else "Trung tính")
            ),
            "action": lambda v: (
                "Ultimate Oscillator trong vùng quá bán, cân nhắc mua."
                if v < 30
                else (
                    "Ultimate Oscillator trong vùng quá mua, cân nhắc bán."
                    if v > 70
                    else "Ultimate Oscillator trên 50, xu hướng tăng nhẹ"
                )
            ),
            "info": "Ultimate Oscillator kết hợp ba khoảng thời gian khác nhau (7, 14, 28) để giảm nhiễu và tạo tín hiệu chính xác hơn. Giá trị dưới 30 là quá bán, trên 70 là quá mua. Có độ tin cậy cao khi xác nhận với xu hướng giá chung.",
        },
        "ROC": {
            "desc": "Rate of Change (14)",
            "signal": lambda v: "Mua" if v > 0 else ("Bán" if v < 0 else "Trung tính"),
            "action": lambda v: (
                "ROC dương, động lượng tăng, cân nhắc mua."
                if v > 0
                else (
                    "ROC âm, động lượng giảm, cân nhắc bán."
                    if v < 0
                    else "ROC âm nhẹ, đà giảm nhẹ, theo dõi thêm"
                )
            ),
            "info": "Rate of Change đo lường tốc độ thay đổi giá theo phần trăm trong 14 phiên. Giá trị dương cao cho thấy tốc độ tăng mạnh (có thể là quá mua), giá trị âm sâu cho thấy tốc độ giảm mạnh (có thể là quá bán). Là chỉ báo động lượng tốt để xác định điểm đảo chiều.",
        },
        "SAR": {
            "desc": "Parabolic SAR",
            "signal": lambda v: "Mua" if v < 0 else "Bán",
            "action": lambda v: (
                "Giá đang nằm trên điểm SAR, xu hướng tăng đang tiếp diễn"
                if v < 0
                else "Giá đang nằm dưới điểm SAR, xu hướng giảm đang tiếp diễn"
            ),
            "info": "Parabolic SAR là chỉ báo theo xu hướng, dùng để xác định điểm dừng và đảo chiều. Điểm SAR nằm dưới giá là tín hiệu mua (xu hướng tăng), điểm SAR nằm trên giá là tín hiệu bán (xu hướng giảm). Khi giá cắt qua điểm SAR, đó là tín hiệu đảo chiều mạnh.",
        },
        "OBV": {
            "desc": "On Balance Volume",
            "signal": lambda v: "Mua" if v > 0 else "Bán",
            "action": lambda v: (
                "OBV tăng xác nhận xu hướng tăng, lực mua mạnh"
                if v > 0
                else "Xác nhận xu hướng giảm, lực bán mạnh"
            ),
            "info": "OBV là chỉ báo cộng dồn khối lượng theo chiều giá. Khi OBV tăng cùng chiều với giá xác nhận xu hướng tăng, ngược chiều với giá tạo phân kỳ cảnh báo đảo chiều.",
        },
        "AO": {
            "desc": "Awesome Oscillator",
            "signal": lambda v: "Mua" if v > 0 else ("Bán" if v < 0 else "Trung tính"),
            "action": lambda v: (
                "AO > 0: Động lượng tăng, cân nhắc mua."
                if v > 0
                else (
                    "AO < 0: Động lượng giảm, cân nhắc bán."
                    if v < 0
                    else "AO âm nhưng tăng, xu hướng giảm đang yếu đi"
                )
            ),
            "info": "Awesome Oscillator so sánh động lượng của 5 phiên gần nhất với 34 phiên. Khi AO vượt qua mức 0 từ dưới lên là tín hiệu mua, từ trên xuống là tín hiệu bán. AO tăng trong vùng dương xác nhận xu hướng tăng, giảm trong vùng âm xác nhận xu hướng giảm.",
        },
        "ADI": {
            "desc": "Accumulation Distribution Index",
            "signal": lambda v: "Mua" if v > 0 else "Bán",
            "action": lambda v: (
                "ADI tăng trong khi giá giảm, phân kỳ tích cực, chuẩn bị đảo chiều tăng"
                if v > 0
                else "ADI giảm, xác nhận xu hướng giảm"
            ),
            "info": "ADI kết hợp giá và khối lượng để đánh giá dòng tiền. Khi ADI tăng cùng chiều với giá là xác nhận xu hướng, ngược chiều là dấu hiệu phân kỳ. Phân kỳ tích cực (giá giảm nhưng ADI tăng) báo hiệu đảo chiều tăng, phân kỳ tiêu cực ngược lại.",
        },
        "BB": {
            "desc": "Bollinger Bands Width (20,2)",
            "signal": lambda v: (
                "Mua" if v < 0.05 else ("Bán" if v > 0.1 else "Trung tính")
            ),
            "action": lambda v: (
                "Giá dưới dải giữa Bollinger Bands, xu hướng giảm nhẹ"
                if v < 0.05
                else (
                    "Giá trên dải giữa Bollinger Bands, xu hướng tăng nhẹ"
                    if v > 0.1
                    else "Giá dao động quanh dải giữa, thị trường sideway"
                )
            ),
            "info": "Bollinger Bands sử dụng độ lệch chuẩn để tạo dải biến động quanh SMA. Giá chạm dải trên thường là tín hiệu quá mua, chạm dải dưới là quá bán. Dải hẹp thường báo hiệu sự tích lũy và sắp có breakout. BB Width đo lường độ rộng của dải, giá trị thấp dự báo biến động mạnh sắp xảy ra.",
        },
        "Momentum": {
            "desc": "Momentum (14)",
            "signal": lambda v: "Mua" if v > 0 else "Bán",
            "action": lambda v: (
                "Động lượng tăng mạnh, ưu tiên mua"
                if v > 0
                else "Động lượng giảm rất mạnh, theo chiều xu hướng giảm"
            ),
            "info": "Momentum đo lường tốc độ thay đổi giá trong 14 phiên. Giá trị dương cao thể hiện xu hướng tăng mạnh, giá trị âm sâu thể hiện xu hướng giảm mạnh. Khi Momentum đạt cực trị và bắt đầu đảo chiều là dấu hiệu sớm cho sự thay đổi xu hướng.",
        },
        "Bear Power": {
            "desc": "Bear Power",
            "signal": lambda v: "Mua" if v > 0 else "Bán",
            "action": lambda v: (
                "Bear Power dương, lực mua mạnh"
                if v > 0
                else "Bear Power âm mạnh, lực bán mạnh, ưu thế thuộc về bên bán"
            ),
            "info": "Bear Power đo lường sức mạnh của phe bán bằng cách so sánh giá thấp nhất với EMA 13 ngày. Giá trị dương cho thấy người mua đang kiểm soát thị trường, giá trị âm cho thấy người bán đang kiểm soát. Khi Bear Power âm nhưng đang tăng dần là tín hiệu mua tiềm năng.",
        },
        "Stochastic": {
            "desc": "Stochastic Oscillator %K (14,3)",
            "signal": lambda v: (
                "Mua" if v < 20 else ("Bán" if v > 80 else "Trung tính")
            ),
            "action": lambda v: (
                "Stochastic trong vùng quá bán, cơ hội mua tốt"
                if v < 20
                else (
                    "Stochastic trong vùng quá mua, cơ hội bán tốt"
                    if v > 80
                    else "Stochastic trung tính, quan sát thêm"
                )
            ),
            "info": "Stochastic Oscillator so sánh giá đóng cửa với phạm vi giá trong một khoảng thời gian nhất định. %K là đường chính, %D là đường tín hiệu. Giá trị dưới 20 là quá bán, trên 80 là quá mua. Tín hiệu mua khi %K cắt lên %D, bán khi %K cắt xuống %D.",
        },
    }
    rule = rules.get(
        name,
        {
            "desc": name,
            "signal": lambda v: "-",
            "action": lambda v: "Không có khuyến nghị.",
            "info": "Không có thông tin.",
        },
    )
    try:
        signal = rule["signal"](value)
    except Exception:
        signal = "-"
    try:
        action = rule["action"](value)
    except Exception:
        action = "Không có khuyến nghị."
    try:
        info = rule["info"]
    except Exception:
        info = "Không có thông tin."
    return {
        "name": name,
        "value": value,
        "signal": signal,
        "action": action,
        "description": rule["desc"],
        "info": info,
    }

# (Repeat for all other formatting, mapping, and export functions)

# ... (move all relevant functions here, with correct imports and type hints) ... 

def format_zones(
    buy_zones, stop_loss_zones, take_profit_zones, current_price
) -> Dict[str, List[Dict[str, Any]]]:
    formatted_buy_zones = []
    for zone in buy_zones:
        price = zone.get("price", 0)
        if ensure_numeric_price(price) < current_price:
            formatted_buy_zones.append(
                {
                    "price": format_price(price),
                    "confidence": zone.get("confidence", "Trung bình"),
                    "reason": zone.get("reason", "Không có lý do"),
                }
            )
    formatted_buy_zones.sort(
        key=lambda x: ensure_numeric_price(x["price"]), reverse=True
    )
    formatted_stop_loss_zones = []
    for zone in stop_loss_zones:
        price = zone.get("price", 0)
        num_price = ensure_numeric_price(price)
        lowest_buy_price = (
            ensure_numeric_price(formatted_buy_zones[-1]["price"])
            if formatted_buy_zones
            else current_price
        )
        if num_price < lowest_buy_price:
            formatted_stop_loss_zones.append(
                {
                    "price": format_price(price),
                    "confidence": zone.get("confidence", "Trung bình"),
                    "reason": zone.get("reason", "Không có lý do"),
                }
            )
    formatted_stop_loss_zones.sort(
        key=lambda x: ensure_numeric_price(x["price"]), reverse=True
    )
    formatted_take_profit_zones = []
    for zone in take_profit_zones:
        price = zone.get("price", 0)
        num_price = ensure_numeric_price(price)
        if num_price > current_price:
            formatted_take_profit_zones.append(
                {
                    "price": format_price(price),
                    "confidence": zone.get("confidence", "Trung bình"),
                    "reason": zone.get("reason", "Không có lý do"),
                }
            )
    formatted_take_profit_zones.sort(key=lambda x: ensure_numeric_price(x["price"]))
    return {
        "buy_zones": formatted_buy_zones,
        "stop_loss_zones": formatted_stop_loss_zones,
        "take_profit_zones": formatted_take_profit_zones,
    }

def risk_reward_processing(risk_reward_ratios, current_price) -> List[Dict[str, Any]]:
    formatted_risk_reward_ratios = []
    for ratio in risk_reward_ratios[:3]:
        buy_price = ratio.get("buy_price", 0)
        stop_loss_price = ratio.get("stop_loss_price", 0)
        take_profit_price = ratio.get("take_profit_price", 0)
        buy_price_num = ensure_numeric_price(buy_price)
        stop_loss_price_num = ensure_numeric_price(stop_loss_price)
        take_profit_price_num = ensure_numeric_price(take_profit_price)
        if (
            buy_price_num > stop_loss_price_num
            and take_profit_price_num > buy_price_num
        ):
            risk = buy_price_num - stop_loss_price_num
            reward = take_profit_price_num - buy_price_num
            risk_reward_ratio = round(reward / risk, 2) if risk > 0 else 0
            quality = "Thấp"
            if risk_reward_ratio >= 3:
                quality = "Tuyệt vời"
            elif risk_reward_ratio >= 2:
                quality = "Tốt"
            elif risk_reward_ratio >= 1.5:
                quality = "Khá"
            elif risk_reward_ratio >= 1:
                quality = "Trung bình"
            formatted_risk_reward_ratios.append(
                {
                    "buy_price": format_price(buy_price),
                    "stop_loss_price": format_price(stop_loss_price),
                    "take_profit_price": format_price(take_profit_price),
                    "ratio": f"{risk_reward_ratio:.2f}",
                    "quality": quality,
                }
            )
    return formatted_risk_reward_ratios

def summary_processing(
    symbol: str, prices: List[PriceData], indicators: Dict[str, Any], volume_spike: bool
) -> str:
    return generate_technical_summary(
        symbol,
        prices,
        indicators.get("sma5", 0),
        indicators.get("sma20", 0),
        indicators.get("macd_hist", 0),
        indicators.get("rsi", 0),
        indicators.get("avg_volume", 0),
        volume_spike,
    )

def generate_technical_summary(
    symbol: str,
    prices: list[PriceData],
    sma5: float,
    sma20: float,
    macd_hist: float,
    rsi: float,
    avg_volume: float,
    volume_spike: bool,
) -> str:
    if not prices:
        return "Không đủ dữ liệu để phân tích kỹ thuật."
    current_price = prices[-1].close_price
    price_change = prices[-1].change_price_percent
    last_trading_date = datetime.fromtimestamp(prices[-1].timestamp).strftime(
        "%Y-%m-%d"
    )
    summary_parts = []
    summary_parts.append(f"=== PHÂN TÍCH KỸ THUẬT {symbol} ({last_trading_date}) ===")
    summary_parts.append("• Khung thời gian: Phân tích hàng ngày (Daily)")
    summary_parts.append(
        f"• Giá hiện tại: {format_price(current_price)} ({price_change:+.2f}%)"
    )
    if current_price > sma5 and current_price > sma20:
        summary_parts.append(
            f"• Giá đang nằm trên cả SMA5 ({format_price(sma5)}) và SMA20 ({format_price(sma20)})"
        )
        summary_parts.append("• Xu hướng tăng ngắn hạn đang chiếm ưu thế")
        if sma5 > sma20:
            summary_parts.append("• SMA5 > SMA20: Tín hiệu tăng giá mạnh")
    elif current_price < sma5 and current_price < sma20:
        summary_parts.append(
            f"• Giá nằm dưới cả SMA5 ({format_price(sma5)}) và SMA20 ({format_price(sma20)})"
        )
        summary_parts.append("• Xu hướng giảm ngắn hạn đang chiếm ưu thế")
        if sma5 < sma20:
            summary_parts.append("• SMA5 < SMA20: Tín hiệu giảm giá mạnh")
    elif current_price > sma5 > sma20:
        summary_parts.append("• Giá vượt SMA5 nhưng chưa vượt SMA20")
        summary_parts.append("• Tín hiệu hồi phục ngắn hạn, cần quan sát thêm")
    elif current_price < sma5 < sma20:
        summary_parts.append("• Giá dưới SMA5 và SMA20")
        summary_parts.append("• Động lực giảm vẫn chiếm ưu thế")
    else:
        summary_parts.append(
            f"• Giá đang dao động quanh các đường trung bình SMA5 ({format_price(sma5)}) và SMA20 ({format_price(sma20)})"
        )
        summary_parts.append("• Thị trường trong trạng thái sideway hoặc tích lũy")
    return "\n".join(summary_parts)

def generate_ma_signals_table(
    current_price: float, ma_values: Dict[str, float]
) -> List[Dict[str, Any]]:
    signals = []
    ma_periods = [5, 10, 20, 50, 100, 200]
    for period in ma_periods:
        sma_key = f"sma{period}"
        ema_key = f"ema{period}"
        if sma_key in ma_values and ma_values[sma_key] is not None:
            sma_value = ma_values[sma_key]
            sma_signal = (
                "Mua"
                if current_price > sma_value
                else "Bán" if current_price < sma_value else "Trung tính"
            )
            signals.append(
                {
                    "period": period,
                    "type": "SMA",
                    "value": format_price(sma_value),
                    "signal": sma_signal,
                }
            )
        if ema_key in ma_values and ma_values[ema_key] is not None:
            ema_value = ma_values[ema_key]
            ema_signal = (
                "Mua"
                if current_price > ema_value
                else "Bán" if current_price < ema_value else "Trung tính"
            )
            signals.append(
                {
                    "period": period,
                    "type": "EMA",
                    "value": format_price(ema_value),
                    "signal": ema_signal,
                }
            )
    return signals

def format_price(price_str: str | float) -> str:
    try:
        price = float(price_str)
        return f"{price:,.0f}"
    except (ValueError, TypeError) as e:
        logging.warning(f"Could not format price '{price_str}': {e}")
        return str(price_str)

def ensure_numeric_price(price_str: str | float) -> float:
    if isinstance(price_str, float) or isinstance(price_str, int):
        return float(price_str)
    elif isinstance(price_str, str):
        try:
            try:
                return float(price_str)
            except ValueError:
                pass
            if "." in price_str and "," not in price_str:
                parts = price_str.split(".")
                if len(parts[-1]) == 3 or all(len(p) == 3 for p in parts[1:]):
                    clean_str = price_str.replace(".", "")
                    return float(clean_str)
                else:
                    return float(price_str)
            elif "," in price_str and "." not in price_str:
                parts = price_str.split(",")
                if len(parts[-1]) == 3 or all(len(p) == 3 for p in parts[1:]):
                    clean_str = price_str.replace(",", "")
                    return float(clean_str)
                else:
                    return float(price_str.replace(",", "."))
            else:
                clean_str = "".join(
                    c for c in price_str if c.isdigit() or c == "." or c == ","
                )
                if "." in clean_str and "," in clean_str:
                    if clean_str.rindex(".") > clean_str.rindex(","):
                        clean_str = clean_str.replace(",", "")
                    else:
                        clean_str = clean_str.replace(".", "").replace(",", ".")
                elif "," in clean_str:
                    clean_str = clean_str.replace(",", ".")
                return float(clean_str)
        except (ValueError, TypeError) as e:
            logging.warning(
                f"Could not convert price '{price_str}' to numeric value: {e}"
            )
            return 0.0 