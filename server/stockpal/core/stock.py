from dataclasses import dataclass
import json


class Stock:
    def __init__(
        self,
        symbol: str,
        name: str,
        exchange: str,
        industry: str,
        sector: str | None = None,
    ):
        self.code = symbol.upper()
        self.name = name
        self.exchange = exchange
        self.industry = industry
        self.sector = sector

    def __str__(self):
        return json.dumps(self.__dict__, indent=2)

    def __repr__(self):
        return json.dumps(self.__dict__, indent=2)


@dataclass
class PriceData:
    symbol: str  # Stock symbol
    timestamp: int  # Original trading date as Unix timestamp
    open_price: float = 0  # Opening price
    close_price: float = 0  # Closing price
    highest_price: float = 0  # Highest price
    lowest_price: float = 0  # Lowest price
    change_price: float = 0  # Change in price (cp)
    change_price_percent: float = 0  # Percentage change in price
    average_price: float = 0  # Average price
    close_price_adjusted: float = 0  # Adjusted closing price
    ceiling_price: float = 0  # Ceiling price (c)
    floor_price: float = 0  # Floor price (f)
    reference_price: float = 0  # Reference price (r)

    match_volume: float = (
        0  # Total matched volume (Đ<PERSON> giao dịch - <PERSON>h<PERSON><PERSON> lượng khớp lệnh)
    )
    match_value: float = 0  # Total matched value (Đ<PERSON> giao dịch - Giá trị khớp lệnh)
    deal_value: float = 0  # Total deal value (Giá trị thỏa thuận)
    deal_volume: float = 0  # Total deal volume (Khối lượng thỏa thuận)

    foreign_current_room: int = 0  # Current foreign room (Room NN)
    foreign_buy_volume: float = 0  # Total foreign buy volume (Khối lượng mua của NN)
    foreign_buy_value: float = 0  # Total foreign buy value (Giá trị mua của NN)
    foreign_sell_volume: float = 0  # Total foreign sell volume (Khối lượng bán của NN)
    foreign_sell_value: float = 0  # Total foreign sell value (Giá trị bán của NN)
    foreign_net_volume: float = (
        0  # Net buy/sell volume (Khối lượng ròng của NN = KL mua - KL bán)
    )
    foreign_net_value: float = (
        0  # Net buy/sell value (Giá trị ròng của NN = GT mua - GT bán)
    )
    foreign_match_buy_volume: float = 0  # Foreign buy volume matched
    foreign_deal_buy_volume: float = 0  # Foreign buy volume deal

    buy_trade_quantity: int = 0  # Total buy trades (Cung cầu - Tổng số lượng đặt mua)
    buy_trade_volume: float = (
        0  # Total buy trade volume (Cung cầu - Tổng khối lượng đặt mua)
    )
    sell_trade_quantity: int = 0  # Total sell trades (Cung cầu - Tổng số lượng bán)
    sell_trade_volume: float = (
        0  # Total sell trade volume (Cung cầu - Tổng khối lượng bán)
    )

    def calc_change_percent(self):
        if self.reference_price != 0:
            self.change_price_percent = round(
                self.change_price / self.reference_price * 100, 2
            )


@dataclass
class DailyPrice(PriceData):
    pass


@dataclass
class MinutePrice(PriceData):
    pass


@dataclass
class EventData:
    id: str
    time: int
    texts: list[str]
    shape: str | None = None
    color: str | None = None
    label: str | None = None
