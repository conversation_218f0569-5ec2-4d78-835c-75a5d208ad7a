class FinancialStatement:
    def __init__(self, income, expenses):
        self.income = income
        self.expenses = expenses

    def calculate_net_income(self):
        net_income = self.income - self.expenses
        return net_income

    def calculate_profit_margin(self):
        if self.income == 0:
            return 0
        profit_margin = (self.income - self.expenses) / self.income
        return profit_margin

    def calculate_operating_margin(self):
        if self.income == 0:
            return 0
        operating_margin = (self.income - self.expenses) / self.income
        return operating_margin
