from typing import List, Dict, Any
from stockpal.core import PriceData
from .base import BaseIndicator

"""
Stochastic RSI (StochRSI) là chỉ báo "động lượng của động lượng", kết hợp ưu điểm của cả
Stochastic Oscillator và Relative Strength Index.

Tín hiệu lực mua/bán của Stochastic RSI:
- Dải giá trị: 0-1 (hoặc 0-100%)
- Lực mua cao: StochRSI > 0.8 (80%)
  + Hành động: <PERSON><PERSON> nhắc bán hoặc thận trọng khi mua
- Lực bán cao: StochRSI < 0.2 (20%)
  + Hành động: Cân nhắc mua với xác suất thành công cao
- Đảo chiều nhanh: StochRSI thay đổi nhanh từ vùng quá mua sang cân bằng
  + Hành động: Tín hiệu cảnh báo bán sớm
"""

class StochasticRSI(BaseIndicator):
    def __init__(self, symbol: str, prices: List[PriceData], rsi_period: int = 14, stoch_period: int = 14, fast_k_period: int = 3, slow_d_period: int = 3):
        """
        Khởi tạo StochasticRSI với các tham số đầy đủ.
        
        Args:
            symbol (str): Mã cổ phiếu
            prices (list[PriceData]): Danh sách dữ liệu giá
            rsi_period (int): Khoảng thời gian tính RSI, mặc định là 14
            stoch_period (int): Khoảng thời gian tính Stochastic, mặc định là 14
            fast_k_period (int): Khoảng thời gian tính Fast %K, mặc định là 3
            slow_d_period (int): Khoảng thời gian tính Slow %D, mặc định là 3
        """
        super().__init__(symbol, prices, rsi_period=rsi_period, stoch_period=stoch_period, fast_k_period=fast_k_period, slow_d_period=slow_d_period)
        self.rsi_period = rsi_period
        self.stoch_period = stoch_period
        self.fast_k_period = fast_k_period
        self.slow_d_period = slow_d_period

    def calculate(self) -> Dict[str, List[float]]:
        """
        Tính giá trị StochasticRSI theo chuẩn Ta-Lib.
        
        Công thức:
        1. Tính RSI với period = rsi_period
        2. Tính StochRSI = (RSI - Min(RSI, stoch_period)) / (Max(RSI, stoch_period) - Min(RSI, stoch_period))
        3. Áp dụng SMA(fast_k_period) cho StochRSI để có Fast %K
        4. Áp dụng SMA(slow_d_period) cho Fast %K để có %D
        
        Returns:
            dict: Từ điển chứa giá trị k_values và d_values của StochasticRSI
        """
        # Đảm bảo đủ dữ liệu để tính toán
        min_data_length = self.rsi_period + self.stoch_period
        if len(self.prices) <= min_data_length:
            return {
                "k_values": [None] * len(self.prices),
                "d_values": [None] * len(self.prices)
            }

        # Sort prices by timestamp in ascending order
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        
        # Bước 1: Tính RSI
        close_prices = [p.close_price for p in sorted_prices]
        rsi_values = [None] * self.rsi_period
        
        for i in range(self.rsi_period, len(sorted_prices)):
            up_sum = 0.0
            down_sum = 0.0
            
            # Tính trung bình tăng/giảm trong rsi_period
            for j in range(i - self.rsi_period + 1, i + 1):
                change = close_prices[j] - close_prices[j - 1]
                if change >= 0:
                    up_sum += change
                else:
                    down_sum += abs(change)
            
            avg_gain = up_sum / self.rsi_period
            avg_loss = down_sum / self.rsi_period
            
            if avg_loss == 0:
                rsi = 100
            else:
                rs = avg_gain / avg_loss
                rsi = 100 - (100 / (1 + rs))
            
            rsi_values.append(rsi)
        
        # Bước 2: Tính StochRSI gốc
        stochrsi_raw = [None] * len(sorted_prices)
        
        for i in range(self.rsi_period + self.stoch_period - 1, len(sorted_prices)):
            rsi_window = rsi_values[i - self.stoch_period + 1:i + 1]
            
            if None in rsi_window:
                continue
                
            min_rsi = min(rsi_window)
            max_rsi = max(rsi_window)
            
            if max_rsi == min_rsi:
                stochrsi_raw[i] = 0.5
            else:
                stochrsi_raw[i] = (rsi_values[i] - min_rsi) / (max_rsi - min_rsi)
        
        # Bước 3: Áp dụng SMA cho Fast %K
        k_values = stochrsi_raw.copy()  # Mặc định nếu fast_k_period = 1
        
        if self.fast_k_period > 1:
            smoothed_k = [None] * len(sorted_prices)
            
            for i in range(self.fast_k_period - 1, len(sorted_prices)):
                window = [v for v in stochrsi_raw[i - self.fast_k_period + 1:i + 1] if v is not None]
                if len(window) == self.fast_k_period:  # Chỉ tính khi đủ dữ liệu
                    smoothed_k[i] = sum(window) / self.fast_k_period
            
            k_values = smoothed_k
        
        # Bước 4: Áp dụng SMA cho %D
        d_values = [None] * len(sorted_prices)
        
        if self.slow_d_period > 1:
            for i in range(self.slow_d_period - 1, len(sorted_prices)):
                window = [v for v in k_values[i - self.slow_d_period + 1:i + 1] if v is not None]
                if len(window) == self.slow_d_period:  # Chỉ tính khi đủ dữ liệu
                    d_values[i] = sum(window) / self.slow_d_period
        
        return {
            "k_values": k_values,
            "d_values": d_values
        }

    def get_signals(self, overbought: float = 0.8, oversold: float = 0.2) -> List[Dict]:
        """
        Xác định tín hiệu dựa trên giá trị StochasticRSI
        
        Args:
            overbought (float): Ngưỡng quá mua (mặc định 0.8 hoặc 80%)
            oversold (float): Ngưỡng quá bán (mặc định 0.2 hoặc 20%)
            
        Returns:
            list[dict]: Danh sách tín hiệu với timestamp, giá trị và hành động gợi ý
        """
        result = self.calculate()
        k_values = result["k_values"]
        d_values = result["d_values"]
        
        signals = []
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        
        for i in range(1, len(sorted_prices)):
            # Bỏ qua nếu không có giá trị hợp lệ
            if i >= len(k_values) or k_values[i] is None or (i > 0 and i-1 < len(k_values) and k_values[i-1] is None):
                continue
                
            k_value = k_values[i]
            d_value = d_values[i] if i < len(d_values) and d_values[i] is not None else None
            prev_k = k_values[i-1]
            
            signal_type = ""
            buying_power = ""
            action = ""
            
            # Vùng quá mua
            if k_value > overbought:
                if prev_k <= overbought:  # Vừa vào vùng quá mua
                    signal_type = "enter_overbought"
                    buying_power = "Lực mua tăng mạnh, vào vùng quá mua"
                    action = "Thận trọng, chuẩn bị cho tín hiệu bán"
                else:
                    signal_type = "overbought"
                    buying_power = "Lực mua cao, thị trường có thể quá mua"
                    action = "Cân nhắc bán hoặc thận trọng khi mua mới"
                
            # Vùng quá bán
            elif k_value < oversold:
                if prev_k >= oversold:  # Vừa vào vùng quá bán
                    signal_type = "enter_oversold"
                    buying_power = "Lực bán tăng mạnh, vào vùng quá bán"
                    action = "Theo dõi, chuẩn bị cho tín hiệu mua"
                else:
                    signal_type = "oversold"
                    buying_power = "Lực bán cao, thị trường có thể quá bán"
                    action = "Cân nhắc mua với xác suất thành công cao"
                
            # Thoát khỏi vùng quá mua
            elif prev_k > overbought and k_value <= overbought:
                signal_type = "exit_overbought"
                buying_power = "Lực mua đang suy yếu, thoát khỏi vùng quá mua"
                action = "Tín hiệu bán mạnh, cân nhắc mở vị thế bán"
                
            # Thoát khỏi vùng quá bán
            elif prev_k < oversold and k_value >= oversold:
                signal_type = "exit_oversold"
                buying_power = "Lực bán đang suy yếu, thoát khỏi vùng quá bán"
                action = "Tín hiệu mua mạnh, cân nhắc mở vị thế mua"
                
            # Crossovers với %D nếu có
            elif d_value is not None:
                if prev_k <= d_values[i-1] and k_value > d_value:
                    signal_type = "bullish_crossover"
                    buying_power = "Lực mua đang tăng"
                    action = "Tín hiệu mua, đặc biệt khi gần vùng quá bán"
                    
                elif prev_k >= d_values[i-1] and k_value < d_value:
                    signal_type = "bearish_crossover"
                    buying_power = "Lực bán đang tăng"
                    action = "Tín hiệu bán, đặc biệt khi gần vùng quá mua"
                
            if signal_type:
                signals.append({
                    "timestamp": sorted_prices[i].timestamp,
                    "price": sorted_prices[i].close_price,
                    "value": round(k_value, 4),
                    "d_value": round(d_value, 4) if d_value is not None else None,
                    "signal": signal_type,
                    "buying_power": buying_power,
                    "action": action
                })
                
        return signals 

    def predict_trend(self) -> Dict[str, Any]:
        result = self.calculate()
        k_values = result["k_values"]
        latest_k = next((v for v in reversed(k_values) if v is not None), 0.0)
        if latest_k > 0.8:
            return {"trend": "overbought", "confidence": 1.0}
        elif latest_k < 0.2:
            return {"trend": "oversold", "confidence": 1.0}
        else:
            return {"trend": "neutral", "confidence": 0.5}

    def get_recommendation(self) -> str:
        result = self.calculate()
        k_values = result["k_values"]
        latest_k = next((v for v in reversed(k_values) if v is not None), 0.0)
        if latest_k > 0.8:
            return "Sell (Overbought)"
        elif latest_k < 0.2:
            return "Buy (Oversold)"
        else:
            return "Hold (Neutral)" 