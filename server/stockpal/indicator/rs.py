from typing import List, Optional, Dict, Any, Union
from stockpal.core import PriceData
from .base import BaseIndicator


class RelativeStrength(BaseIndicator):
    """
    Calculate Relative Strength (RS) indicator for a stock.
    RS compares performance of a stock relative to a benchmark index over a period.

    Args:
        symbol (str): Stock symbol
        prices (List[PriceData]): List of price data for the stock
        benchmark_prices (List[PriceData]): List of price data for the benchmark index
        period (int, optional): The period in weeks to calculate RS. Defaults to 52 (1 year).
    """

    def __init__(
        self,
        symbol: str,
        prices: List[PriceData],
        benchmark_prices: List[PriceData],
        period: int = 52,
    ):
        super().__init__(symbol, prices, benchmark_prices=benchmark_prices, period=period)
        self.benchmark_prices = benchmark_prices
        self.period = period

    def calculate(self) -> Union[List[float], Dict[str, Any]]:
        """
        Calculate the Relative Strength (RS) indicator.

        Returns:
            Union[List[float], Dict[str, Any]]: List of RS values or dictionary with additional data
        """
        # Check if we have enough data
        if len(self.prices) < self.period or len(self.benchmark_prices) < self.period:
            return []

        # Sort prices by timestamp
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        sorted_benchmark = sorted(self.benchmark_prices, key=lambda x: x.timestamp)

        # Create timestamp dict for faster lookup
        benchmark_dict = {p.timestamp: p.close_price for p in sorted_benchmark}

        rs_values = []

        # Calculate RS for each price point where we have enough history
        for i in range(self.period, len(sorted_prices)):
            current_price = sorted_prices[i].close_price
            start_price = sorted_prices[i - self.period].close_price

            if start_price == 0:
                rs_values.append(None)
                continue

            stock_perf = (current_price / start_price) - 1  # Performance in percentage

            # Get benchmark prices for the same timestamps
            current_timestamp = sorted_prices[i].timestamp
            start_timestamp = sorted_prices[i - self.period].timestamp

            if (
                current_timestamp not in benchmark_dict
                or start_timestamp not in benchmark_dict
            ):
                rs_values.append(None)
                continue

            benchmark_current = benchmark_dict[current_timestamp]
            benchmark_start = benchmark_dict[start_timestamp]

            if benchmark_start == 0:
                rs_values.append(None)
                continue

            benchmark_perf = (benchmark_current / benchmark_start) - 1

            # Calculate RS as ratio of performances (add small value to avoid division by zero)
            if benchmark_perf == 0:
                rs = 1.0 if stock_perf >= 0 else 0.0
            else:
                rs = (stock_perf + 1) / (benchmark_perf + 1)

            rs_values.append(rs)

        # Extend the list with None values for periods where we don't have enough history
        rs_values = [None] * self.period + rs_values

        # Make sure we have the same number of values as prices
        if len(rs_values) < len(self.prices):
            rs_values.extend([None] * (len(self.prices) - len(rs_values)))
        elif len(rs_values) > len(self.prices):
            rs_values = rs_values[: len(self.prices)]

        return rs_values

    def get_signals(self) -> List[Dict]:
        # ... (implement if needed, or return empty list)
        return []

    def predict_trend(self) -> Dict[str, Any]:
        rs_values = self.calculate()
        if isinstance(rs_values, list):
            latest_rs = next((v for v in reversed(rs_values) if v is not None), 1.0)
        elif isinstance(rs_values, dict):
            latest_rs = rs_values.get("rs", 1.0)
        else:
            latest_rs = 1.0
        if latest_rs > 1:
            return {"trend": "outperforming", "confidence": min(1.0, latest_rs-1)}
        elif latest_rs < 1:
            return {"trend": "underperforming", "confidence": min(1.0, 1-latest_rs)}
        else:
            return {"trend": "neutral", "confidence": 0.5}

    def get_recommendation(self) -> str:
        rs_values = self.calculate()
        if isinstance(rs_values, list):
            latest_rs = next((v for v in reversed(rs_values) if v is not None), 1.0)
        elif isinstance(rs_values, dict):
            latest_rs = rs_values.get("rs", 1.0)
        else:
            latest_rs = 1.0
        if latest_rs > 1:
            return "Buy (Outperforming Benchmark)"
        elif latest_rs < 1:
            return "Sell (Underperforming Benchmark)"
        else:
            return "Hold (Neutral vs Benchmark)"
