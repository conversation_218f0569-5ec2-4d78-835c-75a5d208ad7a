from typing import List, Dict, Optional, Any
from stockpal.core import PriceData
from .base import BaseIndicator

"""
Accumulation/Distribution Index (ADI) là chỉ báo kỹ thuật đo lường dòng tiền vào và ra khỏi tài sản.
Chỉ báo này tập trung vào mối quan hệ giữa giá và khối lượng để xác định áp lực mua/bán.

Tín hiệu lực mua/bán của ADI:
- Không có dải giá trị chuẩn: Tập trung vào sự biến động và xu hướng
- Phân kỳ dương: Giá giảm nhưng ADI tăng
  + Hành động: Tín hiệu mua tiềm năng
- Phân kỳ âm: Giá tăng nhưng ADI giảm
  + Hành động: Tín hiệu bán tiềm năng
- Sự đồng thuận: ADI và giá di chuyển cùng chiều
  + Hành động: <PERSON>ác nhận xu hướng hiện tại
"""


class AccumulationDistributionIndex(BaseIndicator):
    def __init__(self, symbol: str, prices: List[PriceData], period: int = 14):
        """
        Khởi tạo chỉ báo Accumulation/Distribution Index.

        Args:
            symbol: Mã chứng khoán
            prices: Danh sách dữ liệu giá
            period: Khoảng thời gian cho việc tính toán trung bình (mặc định là 14)
        """
        super().__init__(symbol, prices, period=period)
        self.period = period

    def calculate(self) -> Dict[str, List[Optional[float]]]:
        """
        Tính toán chỉ báo Accumulation/Distribution Index (ADI).

        Returns:
            Dict[str, List[Optional[float]]]: Dictionary chứa giá trị ADI và EMA của ADI
        """
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)

        if len(sorted_prices) < 2:
            return {
                "adi": [None] * len(sorted_prices),
                "adi_ema": [None] * len(sorted_prices)
            }

        # Tính toán Money Flow Multiplier và Money Flow Volume
        mfm_values = []
        mfv_values = []
        
        for price in sorted_prices:
            high = price.highest_price
            low = price.lowest_price
            close = price.close_price
            
            # Lấy khối lượng giao dịch, nếu không có thuộc tính volume thì sử dụng match_volume hoặc giá trị mặc định
            volume = 0
            if hasattr(price, 'volume'):
                volume = price.volume
            elif hasattr(price, 'match_volume'):
                volume = price.match_volume
            
            # Tính toán Money Flow Multiplier
            if high == low:
                mfm = 0
            else:
                mfm = ((close - low) - (high - close)) / (high - low)
            
            # Tính toán Money Flow Volume
            mfv = mfm * volume
            
            mfm_values.append(mfm)
            mfv_values.append(mfv)
        
        # Tính toán ADI (Accumulation/Distribution Line)
        adi_values = [mfv_values[0]]
        for i in range(1, len(mfv_values)):
            adi_values.append(adi_values[i-1] + mfv_values[i])
        
        # Tính toán EMA của ADI
        adi_ema_values = [None] * self.period
        
        # Tính SMA đầu tiên cho EMA
        if len(adi_values) >= self.period:
            sma = sum(adi_values[:self.period]) / self.period
            adi_ema_values.append(sma)
            
            # Tính toán EMA cho các giá trị còn lại
            multiplier = 2 / (self.period + 1)
            for i in range(self.period + 1, len(adi_values)):
                ema = (adi_values[i] - adi_ema_values[-1]) * multiplier + adi_ema_values[-1]
                adi_ema_values.append(ema)
        
        return {
            "adi": adi_values,
            "adi_ema": adi_ema_values
        }

    def get_signals(self) -> List[Dict]:
        """
        Xác định tín hiệu dựa trên giá trị Accumulation/Distribution Index.
        
        Returns:
            List[Dict]: Danh sách tín hiệu với timestamp, giá trị và hành động gợi ý
        """
        result = self.calculate()
        adi_values = result["adi"]
        adi_ema_values = result["adi_ema"]
        
        signals = []
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        
        # Cần ít nhất 5 giá trị để xác định xu hướng
        min_values_needed = 5
        
        for i in range(min_values_needed, len(sorted_prices)):
            if i >= len(adi_values) or adi_values[i] is None:
                continue
            
            # Kiểm tra phân kỳ và đồng thuận
            price_change = sorted_prices[i].close_price - sorted_prices[i-min_values_needed].close_price
            adi_change = adi_values[i] - adi_values[i-min_values_needed]
            
            signal_type = ""
            buying_power = ""
            action = ""
            
            # Phân kỳ dương: Giá giảm nhưng ADI tăng
            if price_change < 0 and adi_change > 0:
                signal_type = "bullish_divergence"
                buying_power = "Lực mua tiềm ẩn đang tăng dù giá giảm"
                action = "Tín hiệu mua tiềm năng, theo dõi các mẫu hình đảo chiều"
            
            # Phân kỳ âm: Giá tăng nhưng ADI giảm
            elif price_change > 0 and adi_change < 0:
                signal_type = "bearish_divergence"
                buying_power = "Lực bán tiềm ẩn đang tăng dù giá tăng"
                action = "Tín hiệu bán tiềm năng, cảnh giác với sự sụt giảm"
            
            # Đồng thuận tăng
            elif price_change > 0 and adi_change > 0:
                signal_type = "bullish_confirmation"
                buying_power = "Lực mua mạnh, đồng thuận với xu hướng tăng giá"
                action = "Xác nhận xu hướng tăng, có thể tiếp tục nắm giữ/mua"
            
            # Đồng thuận giảm
            elif price_change < 0 and adi_change < 0:
                signal_type = "bearish_confirmation"
                buying_power = "Lực bán mạnh, đồng thuận với xu hướng giảm giá"
                action = "Xác nhận xu hướng giảm, cân nhắc bán/không mua vào"
            
            # Kiểm tra cắt EMA
            if i < len(adi_ema_values) and adi_ema_values[i] is not None and i > 0 and adi_ema_values[i-1] is not None:
                # ADI cắt lên trên EMA
                if adi_values[i-1] < adi_ema_values[i-1] and adi_values[i] > adi_ema_values[i]:
                    signal_type += " ema_crossover"
                    buying_power = "Lực mua đang tăng, ADI cắt lên trên đường EMA"
                    action = "Tín hiệu mua, xem xét mở vị thế mua"
                
                # ADI cắt xuống dưới EMA
                elif adi_values[i-1] > adi_ema_values[i-1] and adi_values[i] < adi_ema_values[i]:
                    signal_type += " ema_crossunder"
                    buying_power = "Lực bán đang tăng, ADI cắt xuống dưới đường EMA"
                    action = "Tín hiệu bán, xem xét mở vị thế bán"
            
            if signal_type:
                signals.append({
                    "timestamp": sorted_prices[i].timestamp,
                    "price": sorted_prices[i].close_price,
                    "adi": round(adi_values[i], 2) if adi_values[i] is not None else None,
                    "adi_ema": round(adi_ema_values[i], 2) if i < len(adi_ema_values) and adi_ema_values[i] is not None else None,
                    "signal": signal_type,
                    "buying_power": buying_power,
                    "action": action
                })
        
        return signals 

    def predict_trend(self) -> Dict[str, Any]:
        result = self.calculate()
        adi_values = result["adi"]
        latest_adi = next((v for v in reversed(adi_values) if v is not None), 0.0)
        if latest_adi > 0:
            return {"trend": "uptrend", "confidence": 1.0}
        elif latest_adi < 0:
            return {"trend": "downtrend", "confidence": 1.0}
        else:
            return {"trend": "sideways", "confidence": 0.5}

    def get_recommendation(self) -> str:
        result = self.calculate()
        adi_values = result["adi"]
        latest_adi = next((v for v in reversed(adi_values) if v is not None), 0.0)
        if latest_adi > 0:
            return "Buy (ADI Positive)"
        elif latest_adi < 0:
            return "Sell (ADI Negative)"
        else:
            return "Hold (ADI Neutral)" 