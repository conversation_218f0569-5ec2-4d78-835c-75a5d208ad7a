from stockpal.core import PriceData
from .base import BaseIndicator
from typing import List, Dict, Any

"""
This implementation:

1. Calculates RSI using the standard formula with a default period of 14 days
2. Handles the initial period by returning None values (or 50 if not enough data)
3. Uses the smoothed calculation method for subsequent values
4. Includes a helper method to identify overbought/oversold signals
5. Returns RSI values that can be used for visualization or further analysis

The implementation follows the same pattern as other indicators in your codebase, like the MovingAverage class.

Tín hiệu lực mua/bán của RSI:
- Dải giá trị: 0-100
- Lực mua cao/bán yếu: RSI > 70 (vùng quá mua)
  + Hành động: <PERSON><PERSON> nhắc bán/chốt lời, thận trọng khi mua mới
- Lực bán cao/mua yếu: RSI < 30 (vùng quá bán)
  + Hành động: Cân nhắc mua, thận trọng khi bán
- Thị trường cân bằng: RSI = 40-60
  + Hành động: <PERSON>ợ<PERSON> tín hiệu rõ ràng hơ<PERSON>, theo d<PERSON><PERSON> các chỉ báo khác
"""


class RelativeStrengthIndex(BaseIndicator):
    """
    Relative Strength Index (RSI) indicator implementation.
    Inherits from BaseIndicator and provides calculation, signal, trend, and recommendation methods.
    """
    def __init__(self, symbol: str, prices: List[PriceData], period: int = 14):
        super().__init__(symbol, prices, period=period)
        self.period = period

    def calculate(self) -> List[float]:
        """
        Calculate RSI values for the price data.

        Returns:
            List[float]: List of RSI values corresponding to each price point.
        """
        if len(self.prices) <= self.period:
            return [50.0] * len(self.prices)  # Default value when not enough data

        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        close_prices = [price.close_price for price in sorted_prices]
        price_changes = [
            close_prices[i] - close_prices[i - 1] for i in range(1, len(close_prices))
        ]
        rsi_values = [None] * self.period
        gains = [max(0, change) for change in price_changes[: self.period]]
        losses = [abs(min(0, change)) for change in price_changes[: self.period]]
        avg_gain = sum(gains) / self.period
        avg_loss = sum(losses) / self.period
        if avg_loss == 0:
            rsi_values.append(100)
        else:
            rs = avg_gain / avg_loss
            rsi_values.append(100 - (100 / (1 + rs)))
        for i in range(self.period, len(price_changes)):
            current_gain = max(0, price_changes[i])
            current_loss = abs(min(0, price_changes[i]))
            avg_gain = ((avg_gain * (self.period - 1)) + current_gain) / self.period
            avg_loss = ((avg_loss * (self.period - 1)) + current_loss) / self.period
            if avg_loss == 0:
                rsi_values.append(100)
            else:
                rs = avg_gain / avg_loss
                rsi_values.append(100 - (100 / (1 + rs)))
        rsi_values = [round(v, 2) if v is not None else None for v in rsi_values]
        return rsi_values

    def get_signals(self, overbought: float = 70, oversold: float = 30) -> List[Dict[str, Any]]:
        """
        Identify trading signals based on RSI values.
        Args:
            overbought (float): RSI threshold for overbought condition
            oversold (float): RSI threshold for oversold condition
        Returns:
            List[Dict]: List of signal dictionaries
        """
        rsi_values = self.calculate()
        signals = []
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        for i, rsi in enumerate(rsi_values):
            if rsi is None:
                continue
            if i >= len(sorted_prices):
                break
            signal_type = ""
            buying_power = ""
            action = ""
            if rsi >= overbought:
                signal_type = "overbought"
                buying_power = "Lực mua cao, lực bán yếu"
                action = "Cân nhắc bán/chốt lời, thận trọng khi mua mới"
            elif rsi <= oversold:
                signal_type = "oversold"
                buying_power = "Lực bán cao, lực mua yếu"
                action = "Cân nhắc mua, thận trọng khi bán"
            elif 40 <= rsi <= 60:
                signal_type = "neutral"
                buying_power = "Thị trường cân bằng"
                action = "Đợi tín hiệu rõ ràng hơn"
            elif rsi > 60:
                signal_type = "bullish"
                buying_power = "Lực mua tăng"
                action = "Theo dõi các tín hiệu khác để xác nhận"
            elif rsi < 40:
                signal_type = "bearish"
                buying_power = "Lực bán tăng"
                action = "Theo dõi các tín hiệu khác để xác nhận"
            signals.append({
                "timestamp": sorted_prices[i].timestamp,
                "price": sorted_prices[i].close_price,
                "value": rsi,
                "signal": signal_type,
                "buying_power": buying_power,
                "action": action
            })
        return signals

    def predict_trend(self) -> Dict[str, Any]:
        """
        Predict the trend based on the latest RSI value.
        Returns:
            Dict[str, Any]: Trend direction and confidence
        """
        rsi_values = self.calculate()
        latest_rsi = next((v for v in reversed(rsi_values) if v is not None), 50.0)
        if latest_rsi > 60:
            return {"trend": "uptrend", "confidence": (latest_rsi - 60) / 40}
        elif latest_rsi < 40:
            return {"trend": "downtrend", "confidence": (40 - latest_rsi) / 40}
        else:
            return {"trend": "sideways", "confidence": 1 - abs(latest_rsi - 50) / 10}

    def get_recommendation(self) -> str:
        """
        Generate a recommendation (Buy/Sell/Hold) based on the latest RSI value.
        Returns:
            str: Recommendation string
        """
        rsi_values = self.calculate()
        latest_rsi = next((v for v in reversed(rsi_values) if v is not None), 50.0)
        if latest_rsi >= 70:
            return "Sell (Overbought)"
        elif latest_rsi <= 30:
            return "Buy (Oversold)"
        else:
            return "Hold (Neutral)"
