from typing import List, Dict, Any, Optional
from stockpal.core import PriceData
from .base import BaseIndicator

"""
Bear Power (Sức mạnh gấu) là chỉ báo do bác sĩ <PERSON> Elder phát triển, đo lường hiệu số 
giữa giá thấp nhất và EMA (Exponential Moving Average) trong một khoảng thời gian nhất định.
Chỉ báo này giúp xác định sức mạnh của phe bán (gấu) trong thị trường.

Tín hiệu lực mua/bán của Bear Power:
- Đường 0 là mốc tham chiếu:
  + Bear Power < 0: Sức mạnh gấu thống trị, xu hướng giảm
  + Bear Power > 0: Sức mạnh gấu yếu, xu hướng có thể tăng
- Phân kỳ:
  + Giá tạo đáy thấp hơn nhưng Bear Power tạo đáy cao hơn: Tín hiệu mua tiềm năng
- C<PERSON><PERSON> mức 0:
  + Bear Power từ dưới 0 vượ<PERSON> lên trên 0: <PERSON><PERSON><PERSON> nhận xu hướng tăng
  + Bear Power từ trên 0 giảm xuống dưới 0: Xác nhận xu hướng giảm
"""


class BearPower(BaseIndicator):
    def __init__(self, symbol: str, prices: List[PriceData], period: int = 13):
        """
        Khởi tạo chỉ báo Bear Power.

        Args:
            symbol: Mã chứng khoán
            prices: Danh sách dữ liệu giá
            period: Khoảng thời gian cho EMA (mặc định là 13)
        """
        super().__init__(symbol, prices, period=period)
        self.period = period

    def calculate(self) -> List[Optional[float]]:
        """
        Tính toán chỉ báo Bear Power.

        Returns:
            List[Optional[float]]: Danh sách giá trị Bear Power
        """
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        
        if len(sorted_prices) < self.period:
            return [None] * len(sorted_prices)
        
        # Tính EMA của giá đóng cửa
        close_prices = [price.close_price for price in sorted_prices]
        ema_values = self._calculate_ema(close_prices, self.period)
        
        # Tính Bear Power (Lowest - EMA)
        bear_power_values = []
        
        for i in range(len(sorted_prices)):
            if i < self.period - 1 or ema_values[i] is None:
                bear_power_values.append(None)
            else:
                lowest_price = sorted_prices[i].lowest_price
                bear_power = lowest_price - ema_values[i]
                bear_power_values.append(bear_power)
        
        return bear_power_values

    def _calculate_ema(self, prices: List[float], period: int) -> List[Optional[float]]:
        """
        Tính Exponential Moving Average (EMA).

        Args:
            prices: Danh sách giá đóng cửa
            period: Khoảng thời gian cho EMA

        Returns:
            List[Optional[float]]: Danh sách giá trị EMA
        """
        if len(prices) < period:
            return [None] * len(prices)
        
        # Multiplier: (2 / (period + 1))
        multiplier = 2 / (period + 1)
        
        # Tính SMA đầu tiên
        sma = sum(prices[:period]) / period
        
        ema_values = [None] * (period - 1)
        ema_values.append(sma)
        
        # Tính các giá trị EMA tiếp theo
        for i in range(period, len(prices)):
            ema = (prices[i] - ema_values[-1]) * multiplier + ema_values[-1]
            ema_values.append(ema)
        
        return ema_values

    def get_signals(self) -> List[Dict[str, Any]]:
        """
        Xác định tín hiệu dựa trên giá trị Bear Power.
        
        Returns:
            List[Dict]: Danh sách tín hiệu với timestamp, giá trị và hành động gợi ý
        """
        bear_power_values = self.calculate()
        signals = []
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        
        for i in range(1, len(sorted_prices)):
            if i >= len(bear_power_values) or bear_power_values[i] is None or bear_power_values[i-1] is None:
                continue
            
            current_bear_power = bear_power_values[i]
            prev_bear_power = bear_power_values[i-1]
            
            signal_type = ""
            buying_power = ""
            action = ""
            
            # Tín hiệu xuyên qua đường 0
            if prev_bear_power < 0 and current_bear_power >= 0:
                signal_type = "zero_line_crossover"
                buying_power = "Lực bán đã suy yếu, xu hướng tăng có thể bắt đầu"
                action = "Cơ hội mua tiềm năng, theo dõi xác nhận từ các chỉ báo khác"
                
            elif prev_bear_power > 0 and current_bear_power <= 0:
                signal_type = "zero_line_crossunder"
                buying_power = "Lực bán đang tăng mạnh, xu hướng giảm có thể bắt đầu"
                action = "Cân nhắc bán hoặc không mua vào"
            
            # Tín hiệu dựa trên cực tiểu (đáy)
            elif i >= 5:
                # Kiểm tra xem có phải đáy cục bộ không
                is_local_bottom = True
                for j in range(1, 3):  # Kiểm tra 2 giá trị trước và sau
                    if i-j >= 0 and i-j < len(bear_power_values) and bear_power_values[i-j] is not None:
                        if bear_power_values[i-j] <= current_bear_power:
                            is_local_bottom = False
                            break
                    if i+j < len(bear_power_values) and bear_power_values[i+j] is not None:
                        if bear_power_values[i+j] <= current_bear_power:
                            is_local_bottom = False
                            break
                
                if is_local_bottom and current_bear_power < 0:
                    signal_type = "bear_power_bottom"
                    buying_power = "Lực bán có thể đã đạt đỉnh, cơ hội mua xuất hiện"
                    action = "Cân nhắc mua nếu có xác nhận từ chỉ báo khác"
            
            # Phân kỳ tăng giá (Giá tạo đáy thấp hơn nhưng Bear Power tạo đáy cao hơn)
            if i >= 10:
                # Tìm hai đáy gần nhất của giá và Bear Power
                price_bottoms = []
                bp_bottoms = []
                
                for j in range(i-10, i):
                    if j < 2 or j >= len(bear_power_values) - 2 or bear_power_values[j] is None:
                        continue
                    
                    # Xác định đáy giá
                    if (sorted_prices[j].close_price < sorted_prices[j-1].close_price and 
                        sorted_prices[j].close_price < sorted_prices[j-2].close_price and
                        sorted_prices[j].close_price < sorted_prices[j+1].close_price and
                        sorted_prices[j].close_price < sorted_prices[j+2].close_price):
                        price_bottoms.append((j, sorted_prices[j].close_price))
                    
                    # Xác định đáy Bear Power
                    if (bear_power_values[j] < bear_power_values[j-1] and 
                        bear_power_values[j] < bear_power_values[j-2] and
                        bear_power_values[j] < bear_power_values[j+1] and
                        bear_power_values[j] < bear_power_values[j+2]):
                        bp_bottoms.append((j, bear_power_values[j]))
                
                # Kiểm tra phân kỳ tăng giá
                if len(price_bottoms) >= 2 and len(bp_bottoms) >= 2:
                    price_bottoms = sorted(price_bottoms, key=lambda x: x[0])
                    bp_bottoms = sorted(bp_bottoms, key=lambda x: x[0])
                    
                    if (price_bottoms[-1][1] < price_bottoms[-2][1] and 
                        bp_bottoms[-1][1] > bp_bottoms[-2][1]):
                        signal_type = "bullish_divergence"
                        buying_power = "Lực bán đang suy yếu dù giá vẫn giảm, có thể đảo chiều tăng"
                        action = "Tín hiệu mua mạnh khi có phân kỳ tăng giá"
            
            # Đánh giá sức mạnh gấu
            if current_bear_power < -2:  # Giá trị ngưỡng có thể điều chỉnh
                if "signal_type" not in locals() or not signal_type:
                    signal_type = "strong_bear"
                    buying_power = "Lực bán rất mạnh, thị trường tiếp tục giảm"
                    action = "Không nên mua, có thể xem xét bán"
            elif current_bear_power > 2:  # Giá trị ngưỡng có thể điều chỉnh
                if "signal_type" not in locals() or not signal_type:
                    signal_type = "weak_bear"
                    buying_power = "Lực bán rất yếu, thị trường có thể tăng mạnh"
                    action = "Cơ hội mua tốt, xem xét mở vị thế mua"
            
            if signal_type:
                signals.append({
                    "timestamp": sorted_prices[i].timestamp,
                    "price": sorted_prices[i].close_price,
                    "bear_power": round(current_bear_power, 4),
                    "signal": signal_type,
                    "buying_power": buying_power,
                    "action": action
                })
        
        return signals

    def predict_trend(self) -> Dict[str, Any]:
        bear_power_values = self.calculate()
        latest_bp = next((v for v in reversed(bear_power_values) if v is not None), 0.0)
        if latest_bp > 0:
            return {"trend": "uptrend", "confidence": min(1.0, abs(latest_bp)/10)}
        elif latest_bp < 0:
            return {"trend": "downtrend", "confidence": min(1.0, abs(latest_bp)/10)}
        else:
            return {"trend": "sideways", "confidence": 0.5}

    def get_recommendation(self) -> str:
        bear_power_values = self.calculate()
        latest_bp = next((v for v in reversed(bear_power_values) if v is not None), 0.0)
        if latest_bp > 0:
            return "Buy (Bear Power Positive)"
        elif latest_bp < 0:
            return "Sell (Bear Power Negative)"
        else:
            return "Hold (Bear Power Neutral)" 