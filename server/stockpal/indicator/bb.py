import statistics
from stockpal.core import PriceData
from .base import BaseIndicator
from typing import List, Dict, Any

"""
This implementation of the Bollinger Bands indicator includes:

1. A calculate() method that returns the middle band (SMA), upper band, and lower band values
2. Default parameters of 20 periods for the SMA and 2 standard deviations for the bands
3. Proper handling of the initial period values with None values
4. A get_signals() method that identifies potential trading signals:
- Upper/lower band touches (potential overbought/oversold conditions)
- Band reversals (price moving back inside the bands)
- Middle band crosses (potential trend changes)
5. A get_band_width() method to calculate the Bollinger Band Width, which is useful for identifying volatility

Bollinger Bands are a versatile indicator that can help identify:
- Potential overbought/oversold conditions
- Volatility changes in the market
- Potential trend reversals
- Support and resistance levels

Tín hiệu lực mua/bán của Bollinger Bands:
- Lực mua/bán mạnh:
  + <PERSON><PERSON><PERSON> chạm/vượt dải trên: Lực mua mạnh nhưng có thể quá mua
    * Hành động: <PERSON><PERSON><PERSON><PERSON> trọ<PERSON>, theo dõi dấu hiệu đảo chiều
  + <PERSON><PERSON><PERSON> ch<PERSON>/vượt dải dưới: <PERSON><PERSON><PERSON> bán mạnh nhưng có thể quá bán
    * Hành động: Cơ hội mua tiềm năng, theo dõi dấu hiệu đảo chiều
- Biến động thị trường:
  + Dải băng rộng ra: Biến động tăng
    * Hành động: Thận trọng, rủi ro cao
  + Dải băng thu hẹp: Biến động giảm
    * Hành động: Chuẩn bị cho đợt biến động mạnh sắp tới

The implementation follows the same pattern as other indicators in your codebase and should integrate well with your existing charting functionality.
"""
class BollingerBands(BaseIndicator):
    def __init__(self, symbol: str, prices: List[PriceData], period: int = 20, num_std_dev: float = 2.0):
        super().__init__(symbol, prices, period=period, num_std_dev=num_std_dev)
        self.period = period
        self.num_std_dev = num_std_dev

    def calculate(self) -> Dict[str, List[Any]]:
        """
        Calculate Bollinger Bands for the price data.

        Returns:
            dict: Dictionary containing middle band (SMA), upper band, and lower band values
        """
        if len(self.prices) <= self.period:
            # Not enough data to calculate
            default_value = 0.0
            return {
                "middle_band": [default_value] * len(self.prices),
                "upper_band": [default_value] * len(self.prices),
                "lower_band": [default_value] * len(self.prices),
            }

        # Sort prices by timestamp in ascending order
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)

        # Extract close prices
        close_prices = [price.close_price for price in sorted_prices]

        middle_band = []  # SMA values
        upper_band = []  # Upper band values
        lower_band = []  # Lower band values

        # Calculate initial None values for periods less than the window
        for i in range(self.period - 1):
            middle_band.append(None)
            upper_band.append(None)
            lower_band.append(None)

        # Calculate bands for each window
        for i in range(self.period - 1, len(close_prices)):
            window = close_prices[i - (self.period - 1) : i + 1]

            # Calculate SMA (middle band)
            sma = sum(window) / self.period
            middle_band.append(sma)

            # Calculate standard deviation
            std_dev = statistics.stdev(window)

            # Calculate upper and lower bands
            upper = sma + (self.num_std_dev * std_dev)
            lower = sma - (self.num_std_dev * std_dev)

            upper_band.append(upper)
            lower_band.append(lower)

        return {
            "middle_band": middle_band,
            "upper_band": upper_band,
            "lower_band": lower_band,
        }

    def get_signals(self) -> List[Dict[str, Any]]:
        """
        Xác định tín hiệu tiềm năng dựa trên vị trí giá so với các dải Bollinger.
        
        Returns:
            list[dict]: Danh sách tín hiệu với timestamp, giá trị và hành động gợi ý
        """
        bands = self.calculate()
        middle_band = bands["middle_band"]
        upper_band = bands["upper_band"]
        lower_band = bands["lower_band"]
        
        signals = []
        
        # Sort prices by timestamp in ascending order
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        
        for i in range(self.period, len(sorted_prices)):
            if i >= len(middle_band) or middle_band[i] is None:
                continue
            
            close = sorted_prices[i].close_price
            timestamp = sorted_prices[i].timestamp
            
            signal_type = ""
            buying_power = ""
            action = ""
            
            # Giá chạm hoặc vượt dải trên (tiềm năng bán/quá mua)
            if close >= upper_band[i]:
                signal_type = "upper_band_touch"
                buying_power = "Lực mua mạnh, có thể đang quá mua"
                action = "Thận trọng, theo dõi dấu hiệu đảo chiều để bán"
            
            # Giá chạm hoặc vượt dải dưới (tiềm năng mua/quá bán)
            elif close <= lower_band[i]:
                signal_type = "lower_band_touch"
                buying_power = "Lực bán mạnh, có thể đang quá bán"
                action = "Xem xét mua khi có dấu hiệu đảo chiều"
            
            # Giá quay trở lại trong dải từ trên (tiềm năng đảo chiều)
            elif (i > 0 and i-1 < len(sorted_prices) and 
                  sorted_prices[i-1].close_price >= upper_band[i-1] and 
                  close < upper_band[i]):
                signal_type = "upper_band_reversal"
                buying_power = "Lực bán đang tăng, đảo chiều từ vùng quá mua"
                action = "Tín hiệu bán mạnh, xem xét bán/chốt lời"
            
            # Giá quay trở lại trong dải từ dưới (tiềm năng đảo chiều)
            elif (i > 0 and i-1 < len(sorted_prices) and 
                  sorted_prices[i-1].close_price <= lower_band[i-1] and 
                  close > lower_band[i]):
                signal_type = "lower_band_reversal"
                buying_power = "Lực mua đang tăng, đảo chiều từ vùng quá bán"
                action = "Tín hiệu mua mạnh, xem xét mở vị thế mua"
            
            # Giá cắt dải giữa từ dưới lên (tiềm năng xu hướng tăng)
            elif (i > 0 and i-1 < len(sorted_prices) and i-1 < len(middle_band) and 
                  sorted_prices[i-1].close_price < middle_band[i-1] and 
                  close > middle_band[i]):
                signal_type = "middle_band_cross_up"
                buying_power = "Lực mua tăng, xu hướng tăng đang hình thành"
                action = "Tín hiệu mua trung hạn, xem xét mở vị thế mua"
            
            # Giá cắt dải giữa từ trên xuống (tiềm năng xu hướng giảm)
            elif (i > 0 and i-1 < len(sorted_prices) and i-1 < len(middle_band) and 
                  sorted_prices[i-1].close_price > middle_band[i-1] and 
                  close < middle_band[i]):
                signal_type = "middle_band_cross_down"
                buying_power = "Lực bán tăng, xu hướng giảm đang hình thành"
                action = "Tín hiệu bán trung hạn, xem xét mở vị thế bán"
            
            if signal_type:
                signals.append({
                    "timestamp": timestamp,
                    "price": close,
                    "signal": signal_type,
                    "middle_band": round(middle_band[i], 2),
                    "upper_band": round(upper_band[i], 2),
                    "lower_band": round(lower_band[i], 2),
                    "buying_power": buying_power,
                    "action": action
                })
        
        return signals

    def get_band_width(self) -> list[float]:
        """
        Calculate Bollinger Band Width (upper band - lower band) / middle band.
        Used to identify volatility.

        Returns:
            list[float]: List of band width values
        """
        bands = self.calculate()
        middle_band = bands["middle_band"]
        upper_band = bands["upper_band"]
        lower_band = bands["lower_band"]

        band_width = []

        for i in range(len(middle_band)):
            if middle_band[i] is None or middle_band[i] == 0:
                band_width.append(None)
            else:
                width = (upper_band[i] - lower_band[i]) / middle_band[i]
                band_width.append(width)

        return band_width

    def predict_trend(self) -> Dict[str, Any]:
        bands = self.calculate()
        middle_band = bands["middle_band"]
        latest_middle = next((v for v in reversed(middle_band) if v is not None), 0.0)
        upper_band = bands["upper_band"]
        lower_band = bands["lower_band"]
        latest_upper = next((v for v in reversed(upper_band) if v is not None), 0.0)
        latest_lower = next((v for v in reversed(lower_band) if v is not None), 0.0)
        if latest_middle > latest_upper:
            return {"trend": "uptrend", "confidence": 1.0}
        elif latest_middle < latest_lower:
            return {"trend": "downtrend", "confidence": 1.0}
        else:
            return {"trend": "sideways", "confidence": 0.5}

    def get_recommendation(self) -> str:
        bands = self.calculate()
        middle_band = bands["middle_band"]
        latest_middle = next((v for v in reversed(middle_band) if v is not None), 0.0)
        upper_band = bands["upper_band"]
        lower_band = bands["lower_band"]
        latest_upper = next((v for v in reversed(upper_band) if v is not None), 0.0)
        latest_lower = next((v for v in reversed(lower_band) if v is not None), 0.0)
        if latest_middle > latest_upper:
            return "Sell (Above Upper Band)"
        elif latest_middle < latest_lower:
            return "Buy (Below Lower Band)"
        else:
            return "Hold (Within Bands)"
