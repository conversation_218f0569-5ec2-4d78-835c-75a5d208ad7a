from typing import List, Dict, Any
from stockpal.core import PriceData
from .base import BaseIndicator

"""
This implementation of the On-Balance Volume (OBV) indicator includes:

1. A calculate() method that returns OBV values for each price point
2. A get_signals() method that identifies:
- Trend confirmations (OBV moving in the same direction as price)
- Divergences (OBV moving in the opposite direction as price)
3. A get_breakout_signals() method that identifies:
- OBV breaking above previous high levels (potential bullish signal)
- OBV breaking below previous low levels (potential bearish signal)

The OBV is a momentum indicator that uses volume flow to predict changes in price. It's based on the idea that volume precedes price movements. When OBV is rising, it suggests that volume is flowing into the security (accumulation), which is typically bullish. When OBV is falling, it suggests that volume is flowing out of the security (distribution), which is typically bearish.

Key uses of OBV include:
- Confirming price trends (OBV should move in the same direction as the price)
- Spotting potential reversals through divergences (OBV moving in the opposite direction as price)
- Identifying accumulation/distribution patterns
- Providing early warning signals of potential price movements

Tín hiệu lực mua/bán của OBV:
- Lực mua tăng:
  + OBV tăng cùng chiều với giá (xác nhận xu hướng tăng)
  + OBV tăng trong khi giá giảm (phân kỳ tích cực)
  + Hành động: Cân nhắc mua/giữ
- Lực bán tăng:
  + OBV giảm cùng chiều với giá (xác nhận xu hướng giảm)
  + OBV giảm trong khi giá tăng (phân kỳ tiêu cực)
  + Hành động: Cân nhắc bán/giảm vị thế

The implementation follows the same pattern as other indicators in your codebase and should integrate well with your existing charting functionality.
"""


class OnBalanceVolume(BaseIndicator):
    def __init__(self, symbol: str, prices: List[PriceData]):
        super().__init__(symbol, prices)

    def calculate(self) -> List[float]:
        """
        Calculate On-Balance Volume (OBV) values for the price data.

        Returns:
            list[float]: List of OBV values corresponding to each price point
        """
        if not self.prices:
            return []

        # Sort prices by timestamp in ascending order
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)

        # Initialize OBV list
        obv_values = [0]  # Start with 0 for the first value

        # Calculate OBV for each price point after the first
        for i in range(1, len(sorted_prices)):
            current_close = sorted_prices[i].close_price
            previous_close = sorted_prices[i - 1].close_price
            current_volume = sorted_prices[i].match_volume

            # If close price is higher than previous, add volume
            if current_close > previous_close:
                obv = obv_values[-1] + current_volume
            # If close price is lower than previous, subtract volume
            elif current_close < previous_close:
                obv = obv_values[-1] - current_volume
            # If close price is equal to previous, OBV remains the same
            else:
                obv = obv_values[-1]

            obv_values.append(obv)

        return obv_values

    def get_signals(self, lookback_period: int = 14) -> List[Dict]:
        """
        Xác định tín hiệu dựa trên giá trị OBV, bao gồm xác nhận xu hướng và phân kỳ.
        
        Args:
            lookback_period (int): Khoảng thời gian nhìn lại để phân tích xu hướng
        
        Returns:
            list[dict]: Danh sách tín hiệu với timestamp, giá trị OBV và hành động gợi ý
        """
        obv_values = self.calculate()
        signals = []
        
        # Sort prices by timestamp in ascending order
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        
        # Cần ít nhất lookback_period + 1 giá trị để tạo tín hiệu
        if len(sorted_prices) <= lookback_period:
            return signals
        
        for i in range(lookback_period, len(sorted_prices)):
            # Lấy giá và giá trị OBV hiện tại và trước đó
            current_price = sorted_prices[i].close_price
            previous_price = sorted_prices[i - lookback_period].close_price
            
            if i >= len(obv_values) or i - lookback_period < 0:
                continue
            
            current_obv = obv_values[i]
            previous_obv = obv_values[i - lookback_period]
            
            signal_type = ""
            buying_power = ""
            action = ""
            
            # Giá tăng, OBV tăng (xác nhận xu hướng tăng)
            if current_price > previous_price and current_obv > previous_obv:
                signal_type = "bullish_confirmation"
                buying_power = "Lực mua mạnh, xác nhận xu hướng tăng"
                action = "Mua hoặc giữ vị thế hiện tại"
            
            # Giá giảm, OBV giảm (xác nhận xu hướng giảm)
            elif current_price < previous_price and current_obv < previous_obv:
                signal_type = "bearish_confirmation"
                buying_power = "Lực bán mạnh, xác nhận xu hướng giảm"
                action = "Bán hoặc giảm vị thế hiện tại"
            
            # Giá tăng, OBV giảm (phân kỳ tiêu cực)
            elif current_price > previous_price and current_obv < previous_obv:
                signal_type = "bearish_divergence"
                buying_power = "Lực bán âm thầm tăng, cảnh báo đảo chiều giảm"
                action = "Thận trọng, cân nhắc chốt lời"
            
            # Giá giảm, OBV tăng (phân kỳ tích cực)
            elif current_price < previous_price and current_obv > previous_obv:
                signal_type = "bullish_divergence"
                buying_power = "Lực mua âm thầm tăng, cảnh báo đảo chiều tăng"
                action = "Quan sát, chuẩn bị mua khi có xác nhận"
            
            if signal_type:
                # Chỉ thêm tín hiệu mới nếu khác với tín hiệu trước đó
                if not signals or signals[-1]["signal"] != signal_type:
                    signals.append({
                        "timestamp": sorted_prices[i].timestamp,
                        "price": current_price,
                        "value": current_obv,
                        "signal": signal_type,
                        "buying_power": buying_power,
                        "action": action
                    })
        
        return signals

    def get_breakout_signals(self, lookback_period: int = 20) -> list[dict]:
        """
        Identify breakout signals based on OBV values.

        Args:
            lookback_period (int): Period to look back for high/low OBV values

        Returns:
            list[dict]: List of breakout signals with timestamps
        """
        obv_values = self.calculate()
        signals = []

        # Sort prices by timestamp in ascending order
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)

        # Need at least lookback_period + 1 values to generate signals
        if len(sorted_prices) <= lookback_period:
            return signals

        for i in range(lookback_period, len(sorted_prices)):
            # Get window of OBV values
            obv_window = obv_values[i - lookback_period : i]

            # Find highest and lowest OBV in the window
            highest_obv = max(obv_window)
            lowest_obv = min(obv_window)

            current_obv = obv_values[i]

            signal_type = None

            # OBV breaks above the highest value in the window
            if current_obv > highest_obv:
                signal_type = "obv_breakout_up"

            # OBV breaks below the lowest value in the window
            elif current_obv < lowest_obv:
                signal_type = "obv_breakout_down"

            if signal_type:
                signals.append(
                    {
                        "timestamp": sorted_prices[i].timestamp,
                        "price": sorted_prices[i].close_price,
                        "signal": signal_type,
                        "obv": current_obv,
                    }
                )

        return signals

    def predict_trend(self) -> Dict[str, Any]:
        obv_values = self.calculate()
        latest_obv = next((v for v in reversed(obv_values) if v is not None), 0.0)
        if latest_obv > 0:
            return {"trend": "uptrend", "confidence": 1.0}
        elif latest_obv < 0:
            return {"trend": "downtrend", "confidence": 1.0}
        else:
            return {"trend": "sideways", "confidence": 0.5}

    def get_recommendation(self) -> str:
        obv_values = self.calculate()
        latest_obv = next((v for v in reversed(obv_values) if v is not None), 0.0)
        if latest_obv > 0:
            return "Buy (OBV Positive)"
        elif latest_obv < 0:
            return "Sell (OBV Negative)"
        else:
            return "Hold (OBV Neutral)"
