from typing import List, Dict, Any
from stockpal.core import PriceData
from .base import BaseIndicator

"""
This implementation of the Moving Average (MA) indicator includes:

1. Support for three types of moving averages:
- Simple Moving Average (SMA) - equal weight to all prices
- Exponential Moving Average (EMA) - more weight to recent prices
- Weighted Moving Average (WMA) - linearly increasing weights
2. A calculate() method that returns the MA values for each price point
3. A get_crossover_signals() method that identifies:
- Golden Cross: when a shorter-period MA crosses above a longer-period MA (bullish)
- Death Cross: when a shorter-period MA crosses below a longer-period MA (bearish)
4. A get_price_crossover_signals() method that identifies:
- Bullish Cross: when price crosses above the MA
- Bearish Cross: when price crosses below the MA
5. Proper handling of the initial period values with None values

This implementation follows the same pattern as other indicators in your codebase and provides flexibility with different MA types. The default is a 20-period Simple Moving Average, but you can easily customize the period and type when creating an instance.
"""
class MovingAverage(BaseIndicator):
    def __init__(
        self,
        symbol: str,
        prices: List[PriceData],
        period: int = 20,
        ma_type: str = "simple",
    ):
        super().__init__(symbol, prices, period=period, ma_type=ma_type)
        self.period = period
        self.ma_type = ma_type.lower()  # "simple", "exponential", "weighted"

    def calculate(self) -> List[float]:
        """
        Calculate Moving Average values for the price data.

        Returns:
            list[float]: List of MA values corresponding to each price point
        """
        if len(self.prices) <= self.period:
            return [None] * len(self.prices)

        # Sort prices by timestamp in ascending order
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)

        # Extract close prices
        close_prices = [price.close_price for price in sorted_prices]

        # Initialize MA list with None values for the first (period-1) elements
        ma_values = [None] * (self.period - 1)

        if self.ma_type == "simple":
            # Simple Moving Average (SMA)
            for i in range(self.period - 1, len(close_prices)):
                window = close_prices[i - (self.period - 1) : i + 1]
                ma_values.append(sum(window) / self.period)

        elif self.ma_type == "exponential":
            # Exponential Moving Average (EMA)
            # First EMA is SMA
            window = close_prices[: self.period]
            ema = sum(window) / self.period
            ma_values.append(ema)

            # Calculate multiplier: (2 / (period + 1))
            multiplier = 2 / (self.period + 1)

            # Calculate EMA for remaining prices
            for i in range(self.period, len(close_prices)):
                ema = (close_prices[i] - ema) * multiplier + ema
                ma_values.append(ema)

        elif self.ma_type == "weighted":
            # Weighted Moving Average (WMA)
            for i in range(self.period - 1, len(close_prices)):
                window = close_prices[i - (self.period - 1) : i + 1]

                # Calculate weighted sum (newer values have higher weights)
                numerator = sum((j + 1) * price for j, price in enumerate(window))
                denominator = sum(range(1, self.period + 1))

                ma_values.append(numerator / denominator)
        else:
            # Default to SMA if invalid type
            for i in range(self.period - 1, len(close_prices)):
                window = close_prices[i - (self.period - 1) : i + 1]
                ma_values.append(sum(window) / self.period)

        return ma_values

    def get_signals(self, short_period: int = 9) -> List[Dict]:
        """
        Identify crossover signals between a short-period MA and the current MA.

        Args:
            short_period (int): Period for the shorter MA

        Returns:
            list[dict]: List of crossover signals with timestamps
        """
        # Create a shorter period MA for comparison
        short_ma = MovingAverage(
            symbol=self.symbol,
            prices=self.prices,
            period=short_period,
            ma_type=self.ma_type,
        )

        short_ma_values = short_ma.calculate()
        long_ma_values = self.calculate()

        signals = []

        # Sort prices by timestamp in ascending order
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)

        # Find crossovers (need at least 2 valid values)
        for i in range(1, len(sorted_prices)):
            # Skip if we don't have valid MA values
            if (
                i < self.period
                or i < short_period
                or short_ma_values[i] is None
                or long_ma_values[i] is None
                or short_ma_values[i - 1] is None
                or long_ma_values[i - 1] is None
            ):
                continue

            # Golden Cross: Short MA crosses above Long MA
            if (
                short_ma_values[i - 1] <= long_ma_values[i - 1]
                and short_ma_values[i] > long_ma_values[i]
            ):
                signals.append(
                    {
                        "timestamp": sorted_prices[i].timestamp,
                        "price": sorted_prices[i].close_price,
                        "signal": "golden_cross",
                        "short_ma": short_ma_values[i],
                        "long_ma": long_ma_values[i],
                    }
                )

            # Death Cross: Short MA crosses below Long MA
            elif (
                short_ma_values[i - 1] >= long_ma_values[i - 1]
                and short_ma_values[i] < long_ma_values[i]
            ):
                signals.append(
                    {
                        "timestamp": sorted_prices[i].timestamp,
                        "price": sorted_prices[i].close_price,
                        "signal": "death_cross",
                        "short_ma": short_ma_values[i],
                        "long_ma": long_ma_values[i],
                    }
                )

        return signals

    def get_price_crossover_signals(self) -> list[dict]:
        """
        Identify signals when price crosses above or below the MA.

        Returns:
            list[dict]: List of price crossover signals with timestamps
        """
        ma_values = self.calculate()
        signals = []

        # Sort prices by timestamp in ascending order
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)

        # Find price crossovers (need at least 2 valid values)
        for i in range(1, len(sorted_prices)):
            # Skip if we don't have valid MA values
            if i < self.period or ma_values[i] is None or ma_values[i - 1] is None:
                continue

            current_price = sorted_prices[i].close_price
            prev_price = sorted_prices[i - 1].close_price

            # Bullish: Price crosses above MA
            if prev_price <= ma_values[i - 1] and current_price > ma_values[i]:
                signals.append(
                    {
                        "timestamp": sorted_prices[i].timestamp,
                        "price": current_price,
                        "signal": "bullish_cross",
                        "ma_value": ma_values[i],
                    }
                )

            # Bearish: Price crosses below MA
            elif prev_price >= ma_values[i - 1] and current_price < ma_values[i]:
                signals.append(
                    {
                        "timestamp": sorted_prices[i].timestamp,
                        "price": current_price,
                        "signal": "bearish_cross",
                        "ma_value": ma_values[i],
                    }
                )

        return signals

    def predict_trend(self) -> Dict[str, Any]:
        ma_values = self.calculate()
        latest_ma = next((v for v in reversed(ma_values) if v is not None), 0.0)
        if latest_ma > 0:
            return {"trend": "uptrend", "confidence": 1.0}
        elif latest_ma < 0:
            return {"trend": "downtrend", "confidence": 1.0}
        else:
            return {"trend": "sideways", "confidence": 0.5}

    def get_recommendation(self) -> str:
        ma_values = self.calculate()
        latest_ma = next((v for v in reversed(ma_values) if v is not None), 0.0)
        if latest_ma > 0:
            return "Buy (MA Positive)"
        elif latest_ma < 0:
            return "Sell (MA Negative)"
        else:
            return "Hold (MA Neutral)"
