from typing import List, Dict, Any
from stockpal.core import PriceData
from .base import BaseIndicator

"""
Williams %R là chỉ báo dao động có phạm vi từ 0 đến -100.
Nó đo lường mức độ đóng cửa của giá hiện tại so với mức cao nhất trong khoảng thời gian nhất định.

Tín hiệu lực mua/bán của Williams %R:
- Dải giá trị: -100 đến 0
- Lực mua cao/bán yếu: -20 đến 0 (vùng quá mua)
  + Hành động: Trung tính hoặc thận trọng, đợi xác nhận
- Lực bán cao/mua yếu: -100 đến -80 (vùng quá bán)
  + Hành động: Cân nhắc mua với xác suất cao
- Tín hiệu bán rõ ràng: Williams %R > -10
  + Hành động: C<PERSON> nhắc bán/chốt lời
"""

class WilliamsPercentR(BaseIndicator):
    def __init__(self, symbol: str, prices: List[PriceData], period: int = 14):
        super().__init__(symbol, prices, period=period)
        self.period = period

    def calculate(self) -> List[float]:
        """
        Tính toán giá trị Williams %R.
        Williams %R là chỉ báo kỹ thuật dao động từ 0 đến -100.
        
        Returns:
            list[float]: Danh sách giá trị Williams %R cho mỗi giá
        """
        if len(self.prices) < self.period:
            return [None] * len(self.prices)
            
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        wpr_values = [None] * (self.period - 1)
        
        for i in range(self.period - 1, len(sorted_prices)):
            window = sorted_prices[i - self.period + 1 : i + 1]
            high = max(p.highest_price for p in window)
            low = min(p.lowest_price for p in window)
            close = sorted_prices[i].close_price
            
            if high == low:
                wpr = -50.0  # Giá trị mặc định khi không có biến động
            else:
                # Williams %R = -100 * (HighestHigh - Close) / (HighestHigh - LowestLow)
                wpr = -100 * (high - close) / (high - low)
                
            wpr_values.append(round(wpr, 2))
            
        return wpr_values

    def get_signals(self, overbought: float = -20, oversold: float = -80) -> List[Dict]:
        """
        Xác định tín hiệu dựa trên giá trị Williams %R.
        
        Args:
            overbought (float): Ngưỡng quá mua, thường là -20
            oversold (float): Ngưỡng quá bán, thường là -80
            
        Returns:
            list[dict]: Danh sách tín hiệu với timestamp, giá trị và hành động gợi ý
        """
        wpr_values = self.calculate()
        signals = []
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        
        for i in range(len(sorted_prices)):
            if i >= len(wpr_values) or wpr_values[i] is None:
                continue
                
            wpr = wpr_values[i]
            signal_type = ""
            buying_power = ""
            action = ""
                
            # WPR có giá trị từ 0 đến -100
            if wpr <= oversold:
                signal_type = "oversold"
                buying_power = "Lực bán cao, lực mua yếu"
                action = "Cân nhắc mua với xác suất thành công cao"
            elif wpr >= -10:  # Chỉ khi giá trị rất cao (>= -10) mới xem là tín hiệu bán rõ ràng
                signal_type = "extreme_overbought"
                buying_power = "Lực mua quá cao"
                action = "Tín hiệu bán rõ ràng, cân nhắc chốt lời"
            elif wpr >= overbought:  # Giá trị từ -20 đến -10
                signal_type = "overbought"
                buying_power = "Lực mua cao, lực bán yếu"
                action = "Thận trọng khi mua mới, chuẩn bị cho tín hiệu bán"
            elif wpr > oversold and wpr < overbought:  # Giá trị ở khoảng giữa (-80 đến -20)
                signal_type = "neutral"
                buying_power = "Thị trường cân bằng"
                action = "Đợi tín hiệu rõ ràng hơn"
                
            signals.append({
                "timestamp": sorted_prices[i].timestamp,
                "price": sorted_prices[i].close_price,
                "value": wpr,
                "signal": signal_type,
                "buying_power": buying_power,
                "action": action
            })
            
        return signals 

    def predict_trend(self) -> Dict[str, Any]:
        wpr_values = self.calculate()
        latest_wpr = next((v for v in reversed(wpr_values) if v is not None), 0.0)
        if latest_wpr > -20:
            return {"trend": "overbought", "confidence": 1.0}
        elif latest_wpr < -80:
            return {"trend": "oversold", "confidence": 1.0}
        else:
            return {"trend": "neutral", "confidence": 0.5}

    def get_recommendation(self) -> str:
        wpr_values = self.calculate()
        latest_wpr = next((v for v in reversed(wpr_values) if v is not None), 0.0)
        if latest_wpr > -20:
            return "Sell (Overbought)"
        elif latest_wpr < -80:
            return "Buy (Oversold)"
        else:
            return "Hold (Neutral)" 