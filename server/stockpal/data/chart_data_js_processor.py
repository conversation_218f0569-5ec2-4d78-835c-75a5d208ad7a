from datetime import datetime
import json
import os

from stockpal.core.stock import PriceData
from stockpal.core.util import Utils


class ChartDataJsProcessor:

    def __init__(self, symbol):
        self._symbol = symbol.upper()

    def inject_price_data(self, prices: list[PriceData]):
        # Sort prices by date
        sorted_prices = sorted(prices, key=lambda x: x.timestamp)

        # Format data for lightweight-charts
        close_prices = []
        volumes = []

        for price in sorted_prices:
            date_str = datetime.fromtimestamp(price.timestamp).strftime("%Y-%m-%d")

            # Create price data point
            price_point = {
                "time": date_str,
                "open": price.open_price,
                "high": price.highest_price,
                "low": price.lowest_price,
                "close": price.close_price,
                "volume": price.match_volume,
            }

            if price.close_price > 26000:
                price_point["color"] = "orange"
                price_point["wickColor"] = "orange"

            close_prices.append(price_point)

            # Create volume data point
            volume_point = {"time": date_str, "value": price.match_volume}
            volumes.append(volume_point)

        # Create JavaScript code
        js_code = f"""// Generated chart data for {self._symbol}
const symbol = '{self._symbol}';

const ChartData = {{
    priceHistory: {{
        closePrices: {json.dumps(close_prices, indent=2)},
        volumes: {json.dumps(volumes, indent=2)}
    }}
}}
"""

        # Write to chart-data.js file
        file_path = os.path.join(Utils.get_web_dir(), "chart-data.js")

        with open(file_path, "w") as f:
            f.write(js_code)

        return file_path
