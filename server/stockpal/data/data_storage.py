from datetime import datetime, <PERSON>elta

import xlsxwriter

from stockpal.core import (
    Constants,
    DailyPrice,
    MinutePrice,
    PriceData,
    SqliteService,
    Stock,
    TradingDate,
)
from stockpal.db import DailyPriceDao, MinutePriceDao, SymbolDao, TradingDateDao


class DataStorage:

    def __init__(self, symbol: str):
        self._symbol = symbol.upper()
        self._sql_service = SqliteService()
        self._daily_price_dao = DailyPriceDao(sql_service=self._sql_service)
        self._minute_price_dao = MinutePriceDao(sql_service=self._sql_service)
        self._symbol_dao = SymbolDao(sql_service=self._sql_service)
        self._trading_date_dao = TradingDateDao(sql_service=self._sql_service)

    def get_prices(
        self,
        timeframe_in_minute: bool = False,
        days: int | None = None,
        end_date: datetime | None = None,
    ) -> list[PriceData]:
        """
        L<PERSON>y d<PERSON> liệu gi<PERSON> từ from_ts đến to_ts (nếu có), mặc định to_ts là hiện tại.
        """
        to_ts = (
            int(end_date.timestamp())
            if end_date is not None
            else datetime.now().timestamp()
        )

        if timeframe_in_minute:
            minute_prices = self._minute_price_dao.get_last_number_of_prices(
                symbol=self._symbol, number=days, to_ts=to_ts
            )
            return [MinutePrice(**price.__dict__) for price in minute_prices]

        daily_prices = self._daily_price_dao.get_last_number_of_prices(
            symbol=self._symbol, number=days, to_ts=to_ts
        )
        return [DailyPrice(**price.__dict__) for price in daily_prices]

    def save_prices(self, prices: list[PriceData], timeframe_in_minute: bool = False):
        if timeframe_in_minute:
            minute_prices = [MinutePrice(**price.__dict__) for price in prices]
            self._minute_price_dao.upsert(symbol=self._symbol, prices=minute_prices)
            return

        daily_prices = [DailyPrice(**price.__dict__) for price in prices]
        self._daily_price_dao.upsert(symbol=self._symbol, prices=daily_prices)

    def get_symbols(self) -> list[Stock]:
        return self._symbol_dao.get_all()

    def save_trading_dates(self, prices: list[PriceData]):
        trading_dates = []
        for price in prices:
            # Convert timestamp to date and get day of week
            price_date = datetime.fromtimestamp(price.timestamp).date()
            dow = price_date.weekday()
            trading_dates.append(TradingDate(price_date, dow))

        trading_dates.sort(key=lambda x: x.date)

        self._trading_date_dao.upsert_trading_dates(trading_dates)

    def export_to_excel(
        self,
        prices: list[PriceData],
        timeframe_in_minute: bool = False,
        source_provider: str | None = None,
    ):
        if timeframe_in_minute:
            minute_prices = [MinutePrice(**price.__dict__) for price in prices]

            # Create excel file of day history
            self._minute_price_dao.to_excel(
                symbol=self._symbol,
                suffix_name=source_provider,
                prices=minute_prices,
            )

            return

        daily_prices = [DailyPrice(**price.__dict__) for price in prices]

        # Create excel file of day history
        self._daily_price_dao.to_excel(
            symbol=self._symbol,
            suffix_name=source_provider,
            prices=daily_prices,
        )

    def export_prices_to_excel(self, prices: list[PriceData], to_file: str):
        # Sort by timestamp in descending order
        prices.sort(key=lambda x: x.timestamp, reverse=True)

        workbook = xlsxwriter.Workbook(to_file)
        worksheet = workbook.add_worksheet()

        # Add conditional formatting for positive and negative changes
        worksheet.conditional_format(
            1,
            0,
            len(prices),
            20,
            {
                "type": "formula",
                "criteria": '=INDIRECT("J"&ROW()) > 0',
                "format": workbook.add_format({"bg_color": "#C6EFCE"}),
            },
        )
        worksheet.conditional_format(
            1,
            0,
            len(prices),
            20,
            {
                "type": "formula",
                "criteria": '=INDIRECT("J"&ROW()) < 0',
                "format": workbook.add_format({"bg_color": "#FFC7CE"}),
            },
        )

        # Write data with cell formats
        number_fmt = workbook.add_format({"num_format": "#,##0"})
        decimal_fmt = workbook.add_format({"num_format": "#,##0.00"})
        percent_fmt = workbook.add_format({"num_format": "0.00%"})

        headers = [
            "Thời gian",
            "Mở cửa",
            "Đóng cửa",
            "Cao nhất",
            "Thấp nhất",
            "Trung bình",
            "Tham chiếu",
            "Trần",
            "Sàn",
            "Thay đổi",
            "Thay đổi (%)",
            "Khối lượng",
            "Giá trị giao dịch",
            "NN KL Mua",
            "NN GT Mua",
            "NN KL Bán",
            "NN GT Bán",
            "Room NN",
            "NN KL ròng",
            "NN GT ròng",
            "Tín hiệu",
        ]

        for idx, header in enumerate(headers):
            worksheet.write(0, idx, header)

        for idx, p in enumerate(prices):
            worksheet.write(
                idx + 1,
                0,
                datetime.fromtimestamp(p.timestamp).strftime(
                    "%Y-%m-%d %H:%M:%S" if isinstance(p, MinutePrice) else "%Y-%m-%d"
                ),
            )  # A
            worksheet.write(idx + 1, 1, p.open_price, number_fmt)  # B
            worksheet.write(idx + 1, 2, p.close_price, number_fmt)  # C
            worksheet.write(idx + 1, 3, p.highest_price, number_fmt)  # D
            worksheet.write(idx + 1, 4, p.lowest_price, number_fmt)  # E
            worksheet.write(idx + 1, 5, p.average_price, number_fmt)  # F
            worksheet.write(idx + 1, 6, p.reference_price, number_fmt)  # G
            worksheet.write(idx + 1, 7, p.ceiling_price, number_fmt)  # H
            worksheet.write(idx + 1, 8, p.floor_price, number_fmt)  # I
            worksheet.write(idx + 1, 9, p.change_price, decimal_fmt)  # J
            worksheet.write(idx + 1, 10, p.change_price_percent, percent_fmt)  # K
            worksheet.write(idx + 1, 11, p.match_volume, number_fmt)  # L
            worksheet.write(idx + 1, 12, p.match_value, number_fmt)  # M
            worksheet.write(idx + 1, 13, p.foreign_buy_volume, number_fmt)  # N
            worksheet.write(idx + 1, 14, p.foreign_buy_value, number_fmt)  # O
            worksheet.write(idx + 1, 15, p.foreign_sell_volume, number_fmt)  # P
            worksheet.write(idx + 1, 16, p.foreign_sell_value, number_fmt)  # Q
            worksheet.write(idx + 1, 17, p.foreign_current_room, number_fmt)  # R
            worksheet.write(idx + 1, 18, p.foreign_net_volume, number_fmt)  # S
            worksheet.write(idx + 1, 19, p.foreign_net_value, number_fmt)  # T
            worksheet.write(
                idx + 1,
                20,
                f'=IF(J{idx + 2} > 0, "Tăng", IF(J{idx + 2} < 0, "Giảm", "-"))',
            )

        worksheet.autofit()
        workbook.close()

        print(f"Data exported to {to_file}")
