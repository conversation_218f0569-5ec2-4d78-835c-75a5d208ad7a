from datetime import datetime, timedelta
import os

from stockpal import DataReader
from stockpal.indicator.trend_predictor import TrendPredictor


def main():
    # Get sample data
    symbol = "VNM"  # Example stock symbol
    reader = DataReader(symbol=symbol)
    
    # Get the last 100 days of price data
    end_date = datetime.now()
    start_date = end_date - timedelta(days=100)
    prices = reader.get_prices(start_date=start_date, end_date=end_date)
    
    if not prices:
        print(f"No price data found for {symbol}")
        return
    
    # Create trend predictor
    predictor = TrendPredictor(symbol=symbol, prices=prices)
    
    # Get trend analysis
    trend = predictor.predict_trend()
    print("\n=== TREND ANALYSIS ===")
    print(f"Symbol: {trend['symbol']}")
    print(f"Direction: {trend['direction']}")
    print(f"Strength: {trend['strength']}")
    print(f"Confidence: {trend['confidence']:.2f}%")
    print(f"Bullish signals: {trend['bullish_signals']}")
    print(f"Bearish signals: {trend['bearish_signals']}")
    
    # Get price targets
    targets = predictor.calculate_price_targets()
    print("\n=== PRICE TARGETS ===")
    print(f"Current price: {targets['current_price']}")
    
    print("\nResistance levels:")
    for level in targets['resistance_levels']:
        print(f"  {level['level']:.2f} ({level['source']}, {level['strength']})")
    
    print("\nSupport levels:")
    for level in targets['support_levels']:
        print(f"  {level['level']:.2f} ({level['source']}, {level['strength']})")
    
    # Get trading signals
    signals = predictor.generate_trading_signals()
    print("\n=== TRADING SIGNALS ===")
    print(f"Action: {signals['action'].upper()}")
    print(f"Confidence: {signals['confidence']:.2f}%")
    print(f"Entry price: {signals['entry_price']}")
    print(f"Target price: {signals['target_price']}")
    print(f"Stop loss: {signals['stop_loss']}")
    print(f"Risk-reward ratio: {signals['risk_reward_ratio']}")


if __name__ == "__main__":
    main()