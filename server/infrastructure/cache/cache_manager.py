"""
Cache manager for StockPal application with smart invalidation strategies.

This module provides high-level caching functionality with domain-specific
cache management for stock data, indicators, and analysis results.
"""

import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union

from infrastructure.cache.cache_service import CacheService
from shared.models.stock_models import PricePoint, StockAnalysis, TechnicalIndicator
from shared.exceptions.stock_exceptions import CacheException


logger = logging.getLogger(__name__)


class CacheManager:
    """
    High-level cache manager with domain-specific caching strategies.

    Provides smart caching for:
    - Stock price data with market hours awareness
    - Technical indicators with dependency tracking
    - Analysis results with parameter-based invalidation
    - Symbol lists with periodic refresh
    """

    def __init__(self, cache_service: Optional[CacheService] = None):
        """
        Initialize the cache manager.

        Args:
            cache_service: Cache service instance (creates default if None)
        """
        self.cache_service = cache_service or CacheService()
        self._logger = logging.getLogger(__name__)

        # Cache TTL settings (in seconds)
        self.ttl_settings = {
            "prices_daily": 3600,      # 1 hour for daily prices
            "prices_minute": 300,      # 5 minutes for minute prices
            "indicators": 1800,        # 30 minutes for indicators
            "analysis": 1800,          # 30 minutes for analysis
            "symbols": 86400,          # 24 hours for symbol lists
            "metadata": 3600           # 1 hour for metadata
        }

    # === PRICE DATA CACHING ===

    def get_daily_prices(self, symbol: str, days: int, provider: str) -> Optional[List[PricePoint]]:
        """
        Get cached daily price data.

        Args:
            symbol: Stock symbol
            days: Number of days
            provider: Data provider

        Returns:
            Cached price data or None if not found/expired
        """
        cache_key = f"daily_{symbol}_{days}_{provider}"

        cached_data = self.cache_service.get(cache_key, "prices")
        if cached_data:
            # Convert back to PricePoint objects
            return [PricePoint(**price_data) for price_data in cached_data]

        return None

    def set_daily_prices(self, symbol: str, days: int, provider: str,
                        prices: List[PricePoint]) -> bool:
        """
        Cache daily price data.

        Args:
            symbol: Stock symbol
            days: Number of days
            provider: Data provider
            prices: Price data to cache

        Returns:
            True if successful
        """
        cache_key = f"daily_{symbol}_{days}_{provider}"

        # Convert PricePoint objects to serializable format
        serializable_prices = [price.__dict__ for price in prices]

        # Use shorter TTL during market hours
        ttl = self._get_price_ttl("daily")

        return self.cache_service.set(
            cache_key,
            serializable_prices,
            "prices",
            ttl
        )

    def get_minute_prices(self, symbol: str, provider: str) -> Optional[List[PricePoint]]:
        """Get cached minute price data."""
        cache_key = f"minute_{symbol}_{provider}"

        cached_data = self.cache_service.get(cache_key, "prices")
        if cached_data:
            return [PricePoint(**price_data) for price_data in cached_data]

        return None

    def set_minute_prices(self, symbol: str, provider: str,
                         prices: List[PricePoint]) -> bool:
        """Cache minute price data."""
        cache_key = f"minute_{symbol}_{provider}"
        serializable_prices = [price.__dict__ for price in prices]
        ttl = self._get_price_ttl("minute")

        return self.cache_service.set(
            cache_key,
            serializable_prices,
            "prices",
            ttl
        )

    # === TECHNICAL INDICATOR CACHING ===

    def get_indicator(self, symbol: str, indicator_name: str,
                     parameters: Dict[str, Any]) -> Optional[List[float]]:
        """
        Get cached technical indicator data.

        Args:
            symbol: Stock symbol
            indicator_name: Name of the indicator
            parameters: Indicator parameters

        Returns:
            Cached indicator values or None
        """
        cache_key = self._build_indicator_key(symbol, indicator_name, parameters)

        return self.cache_service.get(cache_key, "indicators")

    def set_indicator(self, symbol: str, indicator_name: str,
                     parameters: Dict[str, Any], values: List[float]) -> bool:
        """
        Cache technical indicator data.

        Args:
            symbol: Stock symbol
            indicator_name: Name of the indicator
            parameters: Indicator parameters
            values: Indicator values to cache

        Returns:
            True if successful
        """
        cache_key = self._build_indicator_key(symbol, indicator_name, parameters)

        return self.cache_service.set(
            cache_key,
            values,
            "indicators",
            self.ttl_settings["indicators"]
        )

    def invalidate_indicators(self, symbol: str) -> int:
        """
        Invalidate all cached indicators for a symbol.

        Args:
            symbol: Stock symbol

        Returns:
            Number of invalidated entries
        """
        # This is a simplified approach - in production, you might want
        # more sophisticated pattern matching
        count = 0

        try:
            # Get all indicator cache files and check if they match the symbol
            indicator_dir = self.cache_service.cache_dir / "indicators"

            if indicator_dir.exists():
                for cache_file in indicator_dir.glob("*.json"):
                    if symbol.lower() in cache_file.name.lower():
                        cache_file.unlink()
                        count += 1

            self._logger.info(f"Invalidated {count} indicator cache entries for {symbol}")

        except Exception as e:
            self._logger.error(f"Failed to invalidate indicators for {symbol}: {str(e)}")

        return count

    # === ANALYSIS RESULT CACHING ===

    def get_analysis(self, symbol: str, analysis_params: Dict[str, Any]) -> Optional[StockAnalysis]:
        """
        Get cached analysis result.

        Args:
            symbol: Stock symbol
            analysis_params: Analysis parameters

        Returns:
            Cached analysis or None
        """
        cache_key = self._build_analysis_key(symbol, analysis_params)

        cached_data = self.cache_service.get(cache_key, "analysis")
        if cached_data:
            # Convert back to StockAnalysis object
            try:
                return StockAnalysis(**cached_data)
            except Exception as e:
                self._logger.warning(f"Failed to deserialize cached analysis: {str(e)}")
                # Invalidate corrupted cache
                self.cache_service.delete(cache_key, "analysis")

        return None

    def set_analysis(self, symbol: str, analysis_params: Dict[str, Any],
                    analysis: StockAnalysis) -> bool:
        """
        Cache analysis result.

        Args:
            symbol: Stock symbol
            analysis_params: Analysis parameters
            analysis: Analysis result to cache

        Returns:
            True if successful
        """
        cache_key = self._build_analysis_key(symbol, analysis_params)

        # Convert to serializable format
        serializable_analysis = analysis.__dict__.copy()

        # Convert datetime objects to ISO strings
        if isinstance(serializable_analysis.get("analysis_date"), datetime):
            serializable_analysis["analysis_date"] = serializable_analysis["analysis_date"].isoformat()
        if isinstance(serializable_analysis.get("last_trading_date"), datetime):
            serializable_analysis["last_trading_date"] = serializable_analysis["last_trading_date"].isoformat()

        # Convert nested objects to dicts
        if "technical_indicators" in serializable_analysis:
            serializable_analysis["technical_indicators"] = [
                indicator.__dict__ if hasattr(indicator, "__dict__") else indicator
                for indicator in serializable_analysis["technical_indicators"]
            ]

        return self.cache_service.set(
            cache_key,
            serializable_analysis,
            "analysis",
            self.ttl_settings["analysis"]
        )

    # === SYMBOL LIST CACHING ===

    def get_symbols(self, provider: str) -> Optional[List[str]]:
        """Get cached symbol list."""
        cache_key = f"symbols_{provider}"
        return self.cache_service.get(cache_key, "symbols")

    def set_symbols(self, provider: str, symbols: List[str]) -> bool:
        """Cache symbol list."""
        cache_key = f"symbols_{provider}"
        return self.cache_service.set(
            cache_key,
            symbols,
            "symbols",
            self.ttl_settings["symbols"]
        )

    # === CACHE MANAGEMENT ===

    def invalidate_symbol_data(self, symbol: str) -> int:
        """
        Invalidate all cached data for a symbol.

        Args:
            symbol: Stock symbol

        Returns:
            Total number of invalidated entries
        """
        total_invalidated = 0

        # Invalidate prices
        total_invalidated += self._invalidate_symbol_in_category(symbol, "prices")

        # Invalidate indicators
        total_invalidated += self.invalidate_indicators(symbol)

        # Invalidate analysis
        total_invalidated += self._invalidate_symbol_in_category(symbol, "analysis")

        self._logger.info(f"Invalidated {total_invalidated} cache entries for {symbol}")
        return total_invalidated

    def cleanup_expired(self) -> int:
        """Clean up expired cache entries."""
        return self.cache_service.cleanup_expired()

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics."""
        base_stats = self.cache_service.get_stats()

        # Add category-specific statistics
        category_stats = {}
        for category in ["prices", "indicators", "analysis", "symbols"]:
            category_dir = self.cache_service.cache_dir / category
            if category_dir.exists():
                files = list(category_dir.glob("*.json"))
                category_stats[category] = {
                    "file_count": len(files),
                    "size_bytes": sum(f.stat().st_size for f in files if f.exists())
                }
            else:
                category_stats[category] = {"file_count": 0, "size_bytes": 0}

        return {
            **base_stats,
            "categories": category_stats
        }

    # === PRIVATE HELPER METHODS ===

    def _get_price_ttl(self, price_type: str) -> int:
        """Get TTL for price data based on market hours."""
        base_ttl = self.ttl_settings.get(f"prices_{price_type}", 3600)

        # During market hours, use shorter TTL for more frequent updates
        now = datetime.now()
        if self._is_market_hours(now):
            return min(base_ttl, 300)  # Max 5 minutes during market hours

        return base_ttl

    def _is_market_hours(self, dt: datetime) -> bool:
        """Check if current time is during market hours (simplified)."""
        # Vietnamese market hours: 9:00 AM - 11:30 AM, 1:00 PM - 3:00 PM
        # This is a simplified check - in production, consider holidays and weekends
        weekday = dt.weekday()
        if weekday >= 5:  # Weekend
            return False

        hour = dt.hour
        minute = dt.minute
        time_minutes = hour * 60 + minute

        # Morning session: 9:00 - 11:30
        morning_start = 9 * 60  # 9:00 AM
        morning_end = 11 * 60 + 30  # 11:30 AM

        # Afternoon session: 13:00 - 15:00
        afternoon_start = 13 * 60  # 1:00 PM
        afternoon_end = 15 * 60  # 3:00 PM

        return (morning_start <= time_minutes <= morning_end or
                afternoon_start <= time_minutes <= afternoon_end)

    def _build_indicator_key(self, symbol: str, indicator_name: str,
                           parameters: Dict[str, Any]) -> str:
        """Build cache key for indicator data."""
        # Sort parameters for consistent key generation
        param_str = "_".join(f"{k}={v}" for k, v in sorted(parameters.items()))
        return f"{symbol}_{indicator_name}_{param_str}"

    def _build_analysis_key(self, symbol: str, analysis_params: Dict[str, Any]) -> str:
        """Build cache key for analysis data."""
        # Include key analysis parameters in the cache key
        key_params = {
            "days_back": analysis_params.get("days_back", 365),
            "indicators": str(sorted(analysis_params.get("indicators", []))),
            "end_date": analysis_params.get("end_date", "").isoformat() if analysis_params.get("end_date") else ""
        }

        param_str = "_".join(f"{k}={v}" for k, v in sorted(key_params.items()) if v)
        return f"{symbol}_analysis_{param_str}"

    def _invalidate_symbol_in_category(self, symbol: str, category: str) -> int:
        """Invalidate all cache entries for a symbol in a specific category."""
        count = 0

        try:
            category_dir = self.cache_service.cache_dir / category

            if category_dir.exists():
                for cache_file in category_dir.glob("*.json"):
                    if symbol.lower() in cache_file.name.lower():
                        cache_file.unlink()
                        count += 1

        except Exception as e:
            self._logger.error(f"Failed to invalidate {category} for {symbol}: {str(e)}")

        return count
