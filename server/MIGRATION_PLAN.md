# Legacy Code Migration Plan

This document outlines the plan for safely migrating from the monolithic structure to the new clean architecture.

## Current State Analysis

### Files to be Deprecated/Removed

#### 1. Monolithic Files (server/stockpal/)
- `data_fetcher.py` - **REPLACE** with `infrastructure/external/data_fetcher.py`
- `stock_analyzer.py` - **REPLACE** with `core/services/analysis_service.py`
- `main.py` - **REPLACE** with `refactored_main.py`

#### 2. Legacy Data Layer (server/stockpal/data/)
- `data_scraper.py` - **KEEP** (used by new infrastructure)
- `data_reader.py` - **REPLACE** with `infrastructure/repositories/price_repository.py`
- `data_storage.py` - **REPLACE** with `infrastructure/repositories/price_repository.py`

#### 3. Legacy Core (server/stockpal/core/)
- `stock.py` - **KEEP** (PriceData models still needed)
- `quote.py` - **KEEP** (used by scrapers)
- `scraper.py` - **KEEP** (base class for scrapers)

#### 4. Legacy Database (server/stockpal/db/)
- All DAO files - **KEEP** (used by new repositories)

## Migration Strategy

### Phase 1: Parallel Operation (Current)
- ✅ New architecture implemented alongside legacy code
- ✅ Both systems can operate independently
- ✅ No breaking changes to existing functionality

### Phase 2: Gradual Migration (Next Steps)
1. **Update Entry Points**
   - Create wrapper functions in legacy files that delegate to new architecture
   - Add deprecation warnings to old functions
   - Update documentation to point to new usage patterns

2. **Data Layer Migration**
   - Replace direct usage of `DataReader` with `PriceRepository`
   - Replace direct usage of `DataStorage` with `PriceRepository`
   - Maintain backward compatibility through adapter pattern

3. **Service Layer Migration**
   - Replace `DataFetcher` usage with `DataFetchingUseCase`
   - Replace `StockAnalyzer` usage with `StockAnalysisUseCase`
   - Provide migration utilities for existing code

### Phase 3: Deprecation (Future)
1. **Mark Legacy Code as Deprecated**
   - Add `@deprecated` decorators
   - Update docstrings with migration instructions
   - Log deprecation warnings when legacy code is used

2. **Remove Legacy Dependencies**
   - Gradually remove imports of legacy modules
   - Update tests to use new architecture
   - Remove legacy configuration options

### Phase 4: Cleanup (Final)
1. **Remove Legacy Files**
   - Delete deprecated files after ensuring no usage
   - Update import statements throughout codebase
   - Clean up configuration and documentation

## Backward Compatibility Strategy

### 1. Adapter Pattern Implementation

Create adapters that maintain the old interface while using new implementation:

```python
# server/stockpal/adapters/legacy_data_fetcher.py
class LegacyDataFetcherAdapter:
    """Adapter to maintain compatibility with old DataFetcher interface."""

    def __init__(self):
        # Initialize new architecture components
        self._use_case = DataFetchingUseCase(...)
        warnings.warn(
            "LegacyDataFetcher is deprecated. Use DataFetchingUseCase instead.",
            DeprecationWarning,
            stacklevel=2
        )

    def fetch_daily_prices(self, symbol: str, days: int = 365):
        """Legacy method - delegates to new use case."""
        return self._use_case.fetch_daily_prices(symbol, days)
```

### 2. Configuration Migration

```python
# server/stockpal/config/migration.py
def migrate_legacy_config(old_config: dict) -> dict:
    """Migrate old configuration format to new architecture."""
    new_config = {}

    # Map old provider names to new enum values
    provider_mapping = {
        "ssi": DataProvider.SSI,
        "vietstock": DataProvider.VIETSTOCK,
        "cafef": DataProvider.CAFEF
    }

    if "default_provider" in old_config:
        new_config["provider"] = provider_mapping.get(
            old_config["default_provider"],
            DataProvider.SSI
        )

    return new_config
```

### 3. Import Compatibility

```python
# server/stockpal/__init__.py
# Maintain old import paths for backward compatibility
from .adapters.legacy_data_fetcher import LegacyDataFetcherAdapter as DataFetcher
from .adapters.legacy_stock_analyzer import LegacyStockAnalyzerAdapter as StockAnalyzer

# Issue deprecation warnings
import warnings
warnings.warn(
    "Direct imports from stockpal are deprecated. "
    "Use application.use_cases instead.",
    DeprecationWarning
)
```

## File-by-File Migration Guide

### 1. data_fetcher.py → DataFetchingUseCase

**Old Usage:**
```python
from stockpal.data_fetcher import DataFetcher
fetcher = DataFetcher()
prices = fetcher.fetch_daily_prices("VIC", 365)
```

**New Usage:**
```python
from server.application.use_cases.data_fetching_use_case import DataFetchingUseCase
from server.core.services.data_service import DataService

data_service = DataService(...)
use_case = DataFetchingUseCase(data_service)
prices = use_case.fetch_daily_prices("VIC", 365)
```

### 2. stock_analyzer.py → StockAnalysisUseCase

**Old Usage:**
```python
from stockpal.stock_analyzer import StockAnalyzer
analyzer = StockAnalyzer()
analysis = analyzer.analyze("VIC", prices)
```

**New Usage:**
```python
from server.application.use_cases.stock_analysis_use_case import StockAnalysisUseCase
from server.core.services.analysis_service import AnalysisService

analysis_service = AnalysisService(...)
use_case = StockAnalysisUseCase(analysis_service)
analysis = use_case.analyze_stock("VIC", prices)
```

### 3. Data Layer Migration

**Old Usage:**
```python
from stockpal.data.data_reader import DataReader
reader = DataReader("VIC")
prices = reader.get_daily_prices(365)
```

**New Usage:**
```python
from server.infrastructure.repositories.price_repository import SqlitePriceRepository
repository = SqlitePriceRepository()
prices = repository.get_daily_prices("VIC", 365)
```

## Testing Strategy

### 1. Parallel Testing
- Run both old and new implementations
- Compare outputs for consistency
- Ensure performance is maintained or improved

### 2. Integration Tests
- Test migration adapters thoroughly
- Verify backward compatibility
- Test error handling in both systems

### 3. Performance Testing
- Benchmark old vs new implementations
- Ensure no performance regressions
- Test memory usage and resource consumption

## Risk Mitigation

### 1. Rollback Plan
- Keep legacy code until new system is proven stable
- Maintain feature flags to switch between implementations
- Have rollback procedures documented

### 2. Monitoring
- Add logging to track usage of legacy vs new code
- Monitor error rates during migration
- Track performance metrics

### 3. Gradual Rollout
- Start with non-critical components
- Migrate high-traffic areas last
- Use feature flags for controlled rollout

## Timeline

### Phase 1: Code Migration & Reorganization (Week 1-2)
- [x] Implement adapter classes
- [x] Create migration utilities
- [x] Set up parallel testing
- [ ] Complete legacy component migration
- [ ] Reorganize indicator implementations
- [ ] Migrate signal synthesizer to new architecture

### Phase 2: Caching Layer Enhancement (Week 3-4)
- [ ] Implement local caching system
- [ ] Add smart cache invalidation
- [ ] Performance optimization for frequently accessed data
- [ ] Cache technical indicators and analysis results

### Phase 3: Advanced Analytics Features (Week 5-6)
- [ ] Implement ML-based trend prediction
- [ ] Add backtesting capabilities
- [ ] Create portfolio optimization features
- [ ] Enhance signal analysis with ML predictions

### Phase 4: End-to-End Testing Suite (Week 7-8)
- [ ] Develop comprehensive integration tests
- [ ] Test data fetching from all providers
- [ ] Validate analysis pipeline
- [ ] Ensure proper error handling

## Success Criteria

1. **Functionality**: All existing features work with new architecture
2. **Performance**: No significant performance degradation
3. **Compatibility**: Legacy code continues to work during transition
4. **Documentation**: Clear migration guide for developers
5. **Testing**: Comprehensive test coverage for new architecture
6. **Monitoring**: Ability to track migration progress and issues

## Post-Migration Benefits

1. **Maintainability**: Cleaner, more modular code structure
2. **Testability**: Better separation of concerns enables easier testing
3. **Scalability**: Architecture supports future enhancements
4. **Performance**: Optimized data access and processing
5. **Reliability**: Better error handling and validation
6. **Developer Experience**: Clearer APIs and better documentation
