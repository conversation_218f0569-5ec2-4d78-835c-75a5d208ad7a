"""
Command handler for data fetching operations.

This module provides command handlers for fetching stock data from external sources.
"""

import logging
from typing import List

from ...shared.models.stock_models import PricePoint, DataProvider
from ...shared.exceptions.stock_exceptions import (
    DataFetchException, SymbolNotFoundException, NetworkException
)
from ...shared.utils.validation import validate_symbol, validate_provider
from ...core.services.data_service import DataService


logger = logging.getLogger(__name__)


class FetchDataCommand:
    """Command for fetching stock data."""

    def __init__(self, data_service: DataService):
        """
        Initialize the command handler.

        Args:
            data_service: Service for data operations
        """
        self._data_service = data_service
        self._logger = logging.getLogger(__name__)

    def fetch_daily_prices(
        self,
        symbol: str,
        days: int = 365,
        provider: DataProvider = DataProvider.SSI,
        force_refresh: bool = False
    ) -> List[PricePoint]:
        """
        Fetch daily price data for a symbol.

        Args:
            symbol: Stock symbol
            days: Number of days to fetch
            provider: Data provider
            force_refresh: Whether to force refresh from external source

        Returns:
            List of price points

        Raises:
            DataFetchException: If data fetching fails
            SymbolNotFoundException: If symbol is not found
        """
        try:
            symbol = validate_symbol(symbol)
            provider = validate_provider(provider)

            self._logger.info(f"Executing fetch daily prices command for {symbol}")

            prices = self._data_service.get_daily_prices(
                symbol=symbol,
                days=days,
                force_refresh=force_refresh
            )

            self._logger.info(f"Successfully fetched {len(prices)} daily prices for {symbol}")
            return prices

        except Exception as e:
            if isinstance(e, (DataFetchException, SymbolNotFoundException)):
                raise

            error_msg = f"Failed to execute fetch daily prices command for {symbol}: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            raise DataFetchException(error_msg)

    def fetch_minute_prices(
        self,
        symbol: str,
        days: int = 5,
        provider: DataProvider = DataProvider.SSI,
        force_refresh: bool = False
    ) -> List[PricePoint]:
        """
        Fetch minute price data for a symbol.

        Args:
            symbol: Stock symbol
            days: Number of days to fetch
            provider: Data provider
            force_refresh: Whether to force refresh from external source

        Returns:
            List of price points

        Raises:
            DataFetchException: If data fetching fails
            SymbolNotFoundException: If symbol is not found
        """
        try:
            symbol = validate_symbol(symbol)
            provider = validate_provider(provider)

            self._logger.info(f"Executing fetch minute prices command for {symbol}")

            prices = self._data_service.get_minute_prices(
                symbol=symbol,
                days=days,
                force_refresh=force_refresh
            )

            self._logger.info(f"Successfully fetched {len(prices)} minute prices for {symbol}")
            return prices

        except Exception as e:
            if isinstance(e, (DataFetchException, SymbolNotFoundException)):
                raise

            error_msg = f"Failed to execute fetch minute prices command for {symbol}: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            raise DataFetchException(error_msg)

    def refresh_all_symbols(
        self,
        provider: DataProvider = DataProvider.SSI,
        max_workers: int = 10
    ) -> dict:
        """
        Refresh price data for all symbols.

        Args:
            provider: Data provider to use
            max_workers: Maximum number of concurrent workers

        Returns:
            Dictionary with refresh results

        Raises:
            DataFetchException: If bulk refresh fails
        """
        try:
            provider = validate_provider(provider)

            self._logger.info(f"Executing refresh all symbols command using {provider}")

            results = self._data_service.refresh_all_symbols(
                provider=provider,
                max_workers=max_workers
            )

            self._logger.info(f"Completed refresh all symbols: {results['message']}")
            return results

        except Exception as e:
            if isinstance(e, DataFetchException):
                raise

            error_msg = f"Failed to execute refresh all symbols command: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            raise DataFetchException(error_msg)

    def fetch_symbols_list(self, provider: DataProvider = DataProvider.SSI) -> List[str]:
        """
        Fetch list of available symbols from provider.

        Args:
            provider: Data provider

        Returns:
            List of available symbols

        Raises:
            DataFetchException: If symbol fetching fails
            NetworkException: If network operation fails
        """
        try:
            provider = validate_provider(provider)

            self._logger.info(f"Executing fetch symbols list command from {provider}")

            # This would need to be implemented in the data service
            # For now, we'll raise an exception indicating it's not implemented
            raise DataFetchException("Fetch symbols list is not yet implemented")

        except Exception as e:
            if isinstance(e, (DataFetchException, NetworkException)):
                raise

            error_msg = f"Failed to execute fetch symbols list command: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            raise DataFetchException(error_msg)

    def validate_data_source(self, provider: DataProvider) -> bool:
        """
        Validate connection to a data source.

        Args:
            provider: Data provider to validate

        Returns:
            True if connection is valid, False otherwise
        """
        try:
            provider = validate_provider(provider)

            self._logger.info(f"Validating data source: {provider}")

            # Try to fetch a small amount of data for validation
            test_symbol = "VIC"  # Use a known symbol for testing

            try:
                prices = self._data_service.get_daily_prices(
                    symbol=test_symbol,
                    days=1,
                    force_refresh=True
                )

                is_valid = len(prices) > 0
                self._logger.info(f"Data source validation for {provider}: {'PASSED' if is_valid else 'FAILED'}")
                return is_valid

            except Exception as validation_error:
                self._logger.warning(f"Data source validation failed for {provider}: {str(validation_error)}")
                return False

        except Exception as e:
            self._logger.error(f"Error during data source validation: {str(e)}")
            return False
