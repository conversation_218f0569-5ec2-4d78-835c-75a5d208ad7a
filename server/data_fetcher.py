from datetime import datetime
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Literal

from stockpal import ChartDataJsProcessor, DataReader, DataScraper, DataStorage, Schema
from stockpal.core.constants import Constants
from stockpal.core.sql_service import SqliteService
from stockpal.data.general_fetcher import GeneralDataFetcher
from stockpal.db import SymbolDao
import logging

logging.basicConfig(
    level=logging.ERROR, format="%(asctime)s - %(levelname)s - %(message)s"
)


def main():
    # Init db schema
    Schema().init()

    # Fetching general data
    GeneralDataFetcher().fetch_all()

    sql_service = SqliteService()
    symbol_dao = SymbolDao(sql_service=sql_service)
    symbols = symbol_dao.get_all()

    # Use ThreadPoolExecutor for parallel processing
    # Limit max workers to avoid overwhelming the system
    max_workers = min(10, len(symbols))
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_symbol = {
            executor.submit(fetch_symbol_data, symbol.code, "ssi"): symbol.code
            for symbol in symbols
        }

        # Process completed tasks
        for future in as_completed(future_to_symbol):
            symbol = future_to_symbol[future]
            try:
                future.result()
                print(f"Successfully fetched data for {symbol}")
            except Exception as e:
                print(f"Error fetching data for {symbol}: {e}")


def fetch_symbol_data(symbol: str, provider: Literal["ssi", "vietstock", "cafef"]):
    scraper = DataScraper(symbol=symbol, provider=provider)
    storage = DataStorage(symbol=symbol)

    try:
        # Fetch prices
        daily_prices = scraper.fetch_prices()
        # Remove prices with close_price = 0
        daily_prices = [price for price in daily_prices if price.close_price != 0]
        # Insert new prices
        storage.save_prices(prices=daily_prices, timeframe_in_minute=False)

        # Fetch all prices to export to excel
        # daily_prices = storage.get_prices(timeframe_in_minute=False)
        # storage.export_prices_to_excel(
        #     prices=daily_prices,
        #     to_file=os.path.join(
        #         "db",
        #         "xlsx",
        #         f"{symbol}_daily_prices_upto_{datetime.now().strftime('%Y_%m_%d')}-{provider}.xlsx",
        #     ),
        # )

        # Save trading dates
        storage.save_trading_dates(daily_prices)
    except Exception as e:
        print(f"Error fetching data for {symbol}: {e}")
        logging.exception("An error occurred during division:")


def generate_chart_data(symbol: str):
    data_reader = DataReader(symbol=symbol)
    daily_prices = data_reader.get_daily_prices(days=200)

    # Generate chart data for web visualization
    chart_processor = ChartDataJsProcessor(symbol=symbol)
    chart_processor.inject_price_data(daily_prices)


if __name__ == "__main__":
    main()
